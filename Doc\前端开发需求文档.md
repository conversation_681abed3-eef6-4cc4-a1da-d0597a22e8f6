# SD卡/U盘开卡测试管理系统 - 前端开发需求文档

## 1. 项目概述

### 1.1 系统定位
前端系统为测试管理平台提供Web用户界面，支持样品管理、数据查询、报表生成等功能的可视化操作。

### 1.2 技术架构
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design 5.x
- **状态管理**: Redux Toolkit + RTK Query
- **路由**: React Router 6
- **图表**: ECharts + echarts-for-react
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **样式**: Less/SCSS + CSS Modules

## 2. 功能模块需求

### 2.1 用户认证模块

#### 2.1.1 登录页面
- **功能要求**:
  - 用户名/密码登录
  - 记住登录状态
  - 登录失败提示
  - 密码强度验证
- **界面设计**:
  - 简洁的登录表单
  - 公司Logo和系统名称
  - 响应式设计适配移动端

#### 2.1.2 权限控制
- **角色管理**: 管理员、测试工程师、质量工程师、访客
- **路由守卫**: 基于角色的页面访问控制
- **按钮权限**: 基于权限的操作按钮显示/隐藏
- **数据权限**: 基于用户角色的数据访问范围

### 2.2 样品管理模块

#### 2.2.1 样品列表页面
```typescript
interface SampleListProps {
  // 查询条件
  searchParams: {
    uid?: string;
    batchNumber?: string;
    model?: string;
    status?: SampleStatus;
    dateRange?: [string, string];
  };
  // 分页参数
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
}
```

- **功能要求**:
  - 样品列表展示 (表格形式)
  - 多条件搜索和筛选
  - 分页和排序
  - 批量操作 (状态更新、删除等)
  - 导出Excel功能

#### 2.2.2 样品详情页面
- **基本信息**: UID、批次号、型号、生产日期等
- **测试历史**: 关联的测试日志列表
- **状态变更**: 样品状态修改和历史记录
- **操作日志**: 样品相关的操作记录

#### 2.2.3 样品创建/编辑页面
- **表单设计**: 使用Ant Design Form组件
- **字段验证**: 必填项、格式验证、唯一性验证
- **批量创建**: 支持Excel导入批量创建样品
- **UID生成**: 自动生成UID并支持手动修改

### 2.3 测试日志管理模块

#### 2.3.1 日志列表页面
- **列表展示**: 日志文件名、关联UID、测试时间、结果等
- **搜索功能**: 按UID、时间范围、测试结果搜索
- **文件预览**: 支持在线预览日志文件内容
- **下载功能**: 支持单个或批量下载日志文件

#### 2.3.2 日志上传页面
- **文件上传**: 
  - 拖拽上传组件
  - 多文件选择上传
  - 上传进度显示
  - 大文件分片上传
- **UID关联**: 
  - 自动识别文件名中的UID
  - 手动选择关联的样品UID
  - 批量UID关联操作

#### 2.3.3 日志详情页面
- **文件信息**: 文件名、大小、上传时间、存储路径
- **解析结果**: 自动解析的测试数据展示
- **关联信息**: 关联的样品信息
- **操作记录**: 文件相关的操作历史

### 2.4 数据分析模块

#### 2.4.1 统计仪表板
```typescript
interface DashboardData {
  // 总体统计
  totalSamples: number;
  totalTests: number;
  passRate: number;
  failRate: number;
  
  // 趋势数据
  dailyStats: Array<{
    date: string;
    testCount: number;
    passCount: number;
    failCount: number;
  }>;
  
  // 批次统计
  batchStats: Array<{
    batchNumber: string;
    sampleCount: number;
    passRate: number;
  }>;
}
```

- **关键指标**: 总样品数、测试通过率、失败率等
- **趋势图表**: 测试数量趋势、通过率趋势
- **分布图表**: 批次分布、状态分布
- **实时更新**: 数据自动刷新机制

#### 2.4.2 高级查询页面
- **查询构建器**: 可视化的查询条件构建
- **多维度分析**: 按时间、批次、型号等维度分析
- **结果展示**: 表格和图表双重展示
- **结果导出**: 支持导出查询结果

### 2.5 报表管理模块

#### 2.5.1 报表生成页面
- **报表类型选择**: 日报、周报、月报、批次报告
- **参数配置**: 时间范围、批次选择、报表模板
- **生成进度**: 实时显示报表生成进度
- **结果预览**: 支持在线预览生成的报表

#### 2.5.2 报表历史页面
- **报表列表**: 历史生成的报表列表
- **搜索筛选**: 按类型、时间、状态筛选
- **在线查看**: 支持在线查看PDF/HTML报表
- **下载管理**: 支持下载各种格式的报表

### 2.6 系统管理模块

#### 2.6.1 用户管理页面
- **用户列表**: 用户信息展示和管理
- **角色分配**: 用户角色的分配和修改
- **权限设置**: 细粒度的权限配置
- **操作日志**: 用户操作记录查看

#### 2.6.2 系统配置页面
- **基础配置**: 系统名称、Logo、联系信息等
- **业务配置**: UID生成规则、测试项目配置
- **存储配置**: NAS存储路径配置
- **通知配置**: 邮件、短信通知配置

## 3. 界面设计规范

### 3.1 设计原则
- **一致性**: 统一的视觉风格和交互模式
- **简洁性**: 界面简洁明了，突出核心功能
- **易用性**: 符合用户习惯的操作流程
- **响应式**: 适配不同屏幕尺寸

### 3.2 色彩规范
```css
:root {
  --primary-color: #1890ff;      /* 主色调 */
  --success-color: #52c41a;      /* 成功色 */
  --warning-color: #faad14;      /* 警告色 */
  --error-color: #f5222d;        /* 错误色 */
  --text-color: #262626;         /* 主文本色 */
  --text-color-secondary: #8c8c8c; /* 次要文本色 */
  --border-color: #d9d9d9;       /* 边框色 */
  --background-color: #f0f2f5;   /* 背景色 */
}
```

### 3.3 布局规范
- **导航栏**: 顶部固定导航，包含Logo、菜单、用户信息
- **侧边栏**: 左侧菜单栏，支持折叠展开
- **内容区**: 主要内容展示区域
- **面包屑**: 页面路径导航
- **页脚**: 版权信息和系统版本

### 3.4 组件规范
- **表格**: 统一的表格样式和交互
- **表单**: 标准的表单布局和验证
- **按钮**: 统一的按钮样式和状态
- **弹窗**: 标准的Modal和Drawer组件

## 4. 技术实现要求

### 4.1 组件架构
```typescript
// 页面组件结构
src/
├── components/          // 通用组件
│   ├── Layout/         // 布局组件
│   ├── Charts/         // 图表组件
│   ├── Upload/         // 上传组件
│   └── Common/         // 其他通用组件
├── pages/              // 页面组件
│   ├── Sample/         // 样品管理页面
│   ├── TestLog/        // 测试日志页面
│   ├── Dashboard/      // 仪表板页面
│   ├── Report/         // 报表页面
│   └── System/         // 系统管理页面
├── hooks/              // 自定义Hooks
├── services/           // API服务
├── store/              // 状态管理
├── utils/              // 工具函数
└── types/              // TypeScript类型定义
```

### 4.2 状态管理
- **全局状态**: 用户信息、权限信息、系统配置
- **页面状态**: 列表数据、表单数据、UI状态
- **缓存策略**: RTK Query自动缓存和更新
- **持久化**: 用户偏好设置本地存储

### 4.3 API集成
```typescript
// API服务示例
export const sampleApi = createApi({
  reducerPath: 'sampleApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/samples',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Sample'],
  endpoints: (builder) => ({
    getSamples: builder.query<SampleListResponse, SampleQueryParams>({
      query: (params) => ({ url: '', params }),
      providesTags: ['Sample'],
    }),
    createSample: builder.mutation<Sample, CreateSampleRequest>({
      query: (body) => ({ url: '', method: 'POST', body }),
      invalidatesTags: ['Sample'],
    }),
  }),
});
```

### 4.4 错误处理
- **网络错误**: 统一的网络错误处理和重试机制
- **业务错误**: 基于错误码的业务错误处理
- **表单验证**: 实时表单验证和错误提示
- **全局异常**: 全局错误边界组件

## 5. 性能优化

### 5.1 加载优化
- **代码分割**: 路由级别的代码分割
- **懒加载**: 组件和图片的懒加载
- **预加载**: 关键资源的预加载
- **缓存策略**: 静态资源缓存和API缓存

### 5.2 渲染优化
- **虚拟滚动**: 大列表的虚拟滚动
- **防抖节流**: 搜索和滚动事件的防抖节流
- **memo优化**: React.memo和useMemo的合理使用
- **状态优化**: 避免不必要的状态更新

### 5.3 用户体验优化
- **加载状态**: 数据加载时的Loading状态
- **骨架屏**: 页面加载时的骨架屏效果
- **错误重试**: 失败操作的重试机制
- **离线提示**: 网络断开时的友好提示

## 6. 测试要求

### 6.1 单元测试
- **组件测试**: 使用React Testing Library
- **工具函数测试**: 使用Jest
- **覆盖率要求**: 核心组件测试覆盖率 > 80%

### 6.2 集成测试
- **API集成测试**: 模拟API响应的集成测试
- **用户流程测试**: 关键用户操作流程测试
- **跨浏览器测试**: 主流浏览器兼容性测试

### 6.3 E2E测试
- **关键流程**: 登录、样品管理、报表生成等关键流程
- **自动化测试**: 使用Playwright或Cypress
- **回归测试**: 版本发布前的回归测试

## 7. 部署与构建

### 7.1 构建配置
- **开发环境**: 热更新、Source Map、开发工具
- **测试环境**: 代码压缩、测试配置
- **生产环境**: 代码混淆、资源优化、CDN配置

### 7.2 部署方案
- **静态部署**: 构建产物部署到Web服务器
- **容器化**: Docker容器化部署
- **CDN加速**: 静态资源CDN加速
- **HTTPS**: 强制HTTPS访问

### 7.3 监控与分析
- **性能监控**: 页面加载时间、API响应时间
- **错误监控**: 前端错误收集和分析
- **用户行为**: 用户操作行为分析
- **访问统计**: 页面访问量统计

## 8. 开发规范

### 8.1 代码规范
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 严格的类型检查
- **Git Hooks**: 提交前的代码检查

### 8.2 命名规范
- **组件**: PascalCase (如: SampleList)
- **文件**: kebab-case (如: sample-list.tsx)
- **变量**: camelCase (如: sampleData)
- **常量**: UPPER_SNAKE_CASE (如: API_BASE_URL)

### 8.3 文档要求
- **组件文档**: 组件使用说明和示例
- **API文档**: 接口调用说明
- **部署文档**: 部署和配置说明
- **更新日志**: 版本更新记录
