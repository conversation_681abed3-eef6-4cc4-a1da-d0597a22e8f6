# SD卡/U盘开卡测试管理系统 - 后端开发需求文档

## 1. 项目概述

### 1.1 系统定位
后端系统作为整个测试管理平台的核心，负责数据存储、业务逻辑处理、API服务提供和NAS存储管理。

### 1.2 技术架构
- **框架**: ASP.NET Core 8.0 Web API
- **ORM**: Entity Framework Core
- **数据库**: SQL Server 2019+ (主库) + Redis (缓存)
- **认证**: ASP.NET Core Identity + JWT
- **日志**: Serilog
- **任务调度**: Hangfire

## 2. 核心功能需求

### 2.1 批次管理API

#### 2.1.1 批次信息管理
```csharp
// API端点设计
POST   /api/batches              // 创建批次
GET    /api/batches              // 查询批次列表
GET    /api/batches/{batchNumber} // 获取批次详情
PUT    /api/batches/{batchNumber} // 更新批次信息
DELETE /api/batches/{batchNumber} // 删除批次
GET    /api/batches/{batchNumber}/statistics // 获取批次统计信息
POST   /api/batches/{batchNumber}/update-statistics // 更新批次统计
```
```csharp

```


#### 2.1.2 UID管理服务
- **UID生成规则**: YYYYMMDD-BATCH-NNNN
- **唯一性验证**: 数据库约束 + 应用层验证
- **UID写入服务**: 提供UID写入样品的接口
- **批量UID生成**: 支持批量生成和分配UID

#### 2.1.3 批次状态管理
```csharp
public enum BatchStatus
{
    待测试 = 0,
    测试中 = 1,
    测试完成 = 2,
    测试失败 = 3,
    已出货 = 4
}
```

### 2.2 测试日志管理API

#### 2.2.1 日志上传接口
```csharp
POST /api/testlogs/upload       // 单个测试记录上传
POST /api/testlogs/upload/batch // 批量测试记录上传
POST /api/testlogs/upload/chunk // 分片上传大文件
GET  /api/testlogs/upload/status/{id} // 上传状态查询
GET  /api/testlogs              // 查询测试日志列表
GET  /api/testlogs/{id}         // 获取测试日志详情
GET  /api/testlogs/sample/{uid} // 获取指定样品的测试记录
GET  /api/testlogs/batch/{batchNumber} // 获取指定批次的测试记录
```

#### 2.2.2 NAS存储集成
- **存储路径管理**: 按年/月/日/批次分层存储
- **文件命名规范**: {UID}_{TestType}_{Timestamp}.{ext}
- **存储服务**: 
  - 文件上传到NAS
  - 文件路径数据库记录
  - 文件访问权限控制
- **存储优化**:
  - 历史文件自动压缩
  - 冷热数据分层
  - 定期清理临时文件

#### 2.2.3 日志解析服务
- **支持格式**: TXT, LOG, CSV, JSON
- **解析引擎**: 可配置的解析规则
- **数据提取**: 测试结果、时间戳、设备信息等
- **异常处理**: 格式错误、数据缺失的处理

### 2.3 数据查询与分析API

#### 2.3.1 查询接口设计
```csharp
GET /api/data/search            // 综合查询
GET /api/data/samples           // 样品查询
GET /api/data/logs              // 日志查询
GET /api/data/statistics        // 统计数据
GET /api/data/trends            // 趋势分析
```

#### 2.3.2 查询参数支持
- **基础查询**: UID、批次、时间范围、状态
- **高级查询**: 多条件组合、模糊查询、正则表达式
- **分页排序**: 支持分页和多字段排序
- **性能优化**: 索引优化、查询缓存

### 2.4 报表生成服务

#### 2.4.1 报表生成API
```csharp
POST /api/reports/generate      // 生成报表
GET  /api/reports/{id}          // 获取报表
GET  /api/reports/{id}/download // 下载报表
GET  /api/reports/templates     // 报表模板管理
```

#### 2.4.2 报表引擎
- **PDF生成**: iText7 for .NET
- **Excel生成**: EPPlus
- **Word生成**: DocumentFormat.OpenXml
- **模板引擎**: Razor Pages
- **异步处理**: Hangfire后台任务
- **进度跟踪**: SignalR实时推送

## 3. 数据模型设计

### 3.1 核心实体模型

#### 3.1.1 批次实体 (Batch)
```csharp
// 批次实体定义
public class Batch
{
    public int Id { get; set; }
    public string BatchNumber { get; set; }   // 批次号
    public string BatchName { get; set; }     // 批次名称
    public string Model { get; set; }         // 型号
    public DateTime ProductionDate { get; set; } // 入库日期
    public string QualityGrade { get; set; }  // 质量等级
    public string Supplier { get; set; }      // 供应商
    public int PlannedQuantity { get; set; }  // 计划数量
    public int ActualQuantity { get; set; }   // 实际数量
    public int TestedQuantity { get; set; }   // 已测试数量
    public int PassedQuantity { get; set; }   // 通过数量
    public int FailedQuantity { get; set; }   // 失败数量
    public long? ExpectedCapacity { get; set; } // 预期容量
    public string Description { get; set; }   // 批次描述
    public string OtherInfo { get; set; }     // 其他信息
    public BatchStatus Status { get; set; }   // 状态
    public DateTime? StartDate { get; set; }  // 开始测试日期
    public DateTime? EndDate { get; set; }    // 完成测试日期
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; }     // 创建人
    public string UpdatedBy { get; set; }     // 更新人

    // 导航属性
    public ICollection<TestLog> TestLogs { get; set; }

    // 计算属性
    public decimal PassRate => TestedQuantity > 0 ? (decimal)PassedQuantity / TestedQuantity * 100 : 0;
    public bool IsCompleted => Status == BatchStatus.测试完成 || Status == BatchStatus.已出货;
}

public enum BatchStatus
{
    待测试 = 0,
    测试中 = 1,
    测试完成 = 2,
    测试失败 = 3,
    已出货 = 4
}
```

#### 3.1.2 测试日志实体 (TestLog)
```csharp
public class TestLog
{
    public long Id { get; set; }
    public string SampleUID { get; set; }     // 单片样品UID
    public string BatchNumber { get; set; }   // 关联批次号
    public string LogFileName { get; set; }   // 日志文件名
    public string NASFilePath { get; set; }   // NAS存储路径
    public string TestType { get; set; }      // 测试类型
    public DateTime TestTime { get; set; }    // 测试时间
    public bool TestResult { get; set; }      // 测试结果
    public string TestData { get; set; }      // 测试数据JSON
    public string ErrorMessage { get; set; }  // 错误信息
    public long? FileSize { get; set; }       // 文件大小
    public string FileChecksum { get; set; }  // 文件校验和
    public string DeviceInfo { get; set; }    // 设备信息
    public string StationId { get; set; }     // 测试工位ID
    public string OperatorId { get; set; }    // 操作员ID
    public int? TestDuration { get; set; }    // 测试耗时(秒)
    public long? ActualCapacity { get; set; } // 实际容量(字节)
    public decimal? ReadSpeed { get; set; }   // 读取速度(MB/s)
    public decimal? WriteSpeed { get; set; }  // 写入速度(MB/s)
    public int? BadBlockCount { get; set; }   // 坏块数量
    public DateTime CreatedAt { get; set; }

    // 导航属性
    public Batch Batch { get; set; }

    // 计算属性
    public string TestResultText => TestResult ? "通过" : "失败";
    public string CapacityGB => ActualCapacity.HasValue ? $"{ActualCapacity.Value / (1024.0 * 1024.0 * 1024.0):F2} GB" : "未知";
}
```

### 3.2 数据库表设计

#### 3.2.1 批次表 (Batches) - 主要样品管理表
```sql
CREATE TABLE Batches (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    BatchNumber NVARCHAR(50) NOT NULL UNIQUE,  -- 批次号
    BatchName NVARCHAR(200),                   -- 批次名称
    Model NVARCHAR(100) NOT NULL,              -- 型号
    ProductionDate DATETIME2 NOT NULL,         -- 入库日期
    QualityGrade NVARCHAR(20),                 -- 质量等级
    Supplier NVARCHAR(200),                    -- 供应商
    PlannedQuantity INT NOT NULL,              -- 计划数量
    ActualQuantity INT DEFAULT 0,              -- 实际数量
    TestedQuantity INT DEFAULT 0,              -- 已测试数量
    PassedQuantity INT DEFAULT 0,              -- 通过数量
    FailedQuantity INT DEFAULT 0,              -- 失败数量
    ExpectedCapacity BIGINT,                   -- 预期容量(字节)
    Description NVARCHAR(1000),                -- 批次描述
    OtherInfo NVARCHAR(MAX),                   -- 其他信息
    Status INT NOT NULL DEFAULT 0,             -- 状态 (0:待测试, 1:测试中, 2:测试完成, 3:测试失败, 4:已出货)
    StartDate DATETIME2,                       -- 开始测试日期
    EndDate DATETIME2,                         -- 完成测试日期
    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(100),                   -- 创建人
    UpdatedBy NVARCHAR(100),                   -- 更新人

    -- 索引
    INDEX IX_Batches_BatchNumber (BatchNumber),
    INDEX IX_Batches_Model (Model),
    INDEX IX_Batches_Status (Status),
    INDEX IX_Batches_ProductionDate (ProductionDate),
    INDEX IX_Batches_CreatedAt (CreatedAt),
    INDEX IX_Batches_Supplier (Supplier)
);
```

#### 3.2.2 测试日志表 (TestLogs) - 每片样品的测试记录
```sql
CREATE TABLE TestLogs (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    SampleUID NVARCHAR(50) NOT NULL,            -- 单片样品UID (格式: YYYYMMDD-BATCH-NNNN)
    BatchNumber NVARCHAR(50) NOT NULL,          -- 关联批次号
    LogFileName NVARCHAR(500) NOT NULL,         -- 日志文件名
    NASFilePath NVARCHAR(1000) NOT NULL,        -- NAS存储路径
    TestType NVARCHAR(50) NOT NULL,             -- 测试类型 (FULL_TEST, QUICK_TEST, CAPACITY_TEST等)
    TestTime DATETIME2 NOT NULL,                -- 测试时间
    TestResult BIT NOT NULL,                    -- 测试结果 (0:失败, 1:通过)
    TestData NVARCHAR(MAX),                     -- 测试数据JSON
    ErrorMessage NVARCHAR(MAX),                 -- 错误信息
    FileSize BIGINT,                            -- 文件大小
    FileChecksum NVARCHAR(64),                  -- 文件校验和
    DeviceInfo NVARCHAR(500),                   -- 设备信息
    StationId NVARCHAR(100),                    -- 测试工位ID
    OperatorId NVARCHAR(100),                   -- 操作员ID
    TestDuration INT,                           -- 测试耗时(秒)
    ActualCapacity BIGINT,                      -- 实际容量(字节)
    ReadSpeed DECIMAL(10,2),                    -- 读取速度(MB/s)
    WriteSpeed DECIMAL(10,2),                   -- 写入速度(MB/s)
    BadBlockCount INT,                          -- 坏块数量
    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),

    -- 外键约束
    CONSTRAINT FK_TestLogs_Batches FOREIGN KEY (BatchNumber) REFERENCES Batches(BatchNumber),

    -- 索引
    INDEX IX_TestLogs_SampleUID (SampleUID),
    INDEX IX_TestLogs_BatchNumber (BatchNumber),
    INDEX IX_TestLogs_TestTime (TestTime),
    INDEX IX_TestLogs_TestResult (TestResult),
    INDEX IX_TestLogs_TestType (TestType),
    INDEX IX_TestLogs_CreatedAt (CreatedAt),
    INDEX IX_TestLogs_StationId (StationId)
);
```

#### 3.2.3 用户表 (Users)
```sql
CREATE TABLE Users (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(100) NOT NULL UNIQUE,    -- 用户名
    Email NVARCHAR(200) NOT NULL UNIQUE,       -- 邮箱
    PasswordHash NVARCHAR(500) NOT NULL,       -- 密码哈希
    Salt NVARCHAR(100) NOT NULL,               -- 密码盐值
    FullName NVARCHAR(200) NOT NULL,           -- 全名
    RoleId INT NOT NULL,                       -- 角色ID
    IsActive BIT NOT NULL DEFAULT 1,           -- 是否激活
    LastLoginTime DATETIME2,                   -- 最后登录时间
    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),

    -- 外键约束
    CONSTRAINT FK_Users_Roles FOREIGN KEY (RoleId) REFERENCES Roles(Id),

    -- 索引
    INDEX IX_Users_Username (Username),
    INDEX IX_Users_Email (Email),
    INDEX IX_Users_RoleId (RoleId),
    INDEX IX_Users_IsActive (IsActive)
);
```

#### 3.2.4 角色表 (Roles)
```sql
CREATE TABLE Roles (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    RoleName NVARCHAR(100) NOT NULL UNIQUE,    -- 角色名称
    Description NVARCHAR(500),                 -- 角色描述
    Permissions NVARCHAR(MAX),                 -- 权限JSON
    IsActive BIT NOT NULL DEFAULT 1,           -- 是否激活
    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
);

-- 插入默认角色
INSERT INTO Roles (RoleName, Description, Permissions) VALUES
('管理员', '系统管理员，拥有所有权限', '["*"]'),
('测试工程师', '测试工程师，负责测试数据录入和查询', '["sample:read", "sample:create", "sample:update", "testlog:read", "testlog:create"]'),
('质量工程师', '质量工程师，负责数据分析和报表生成', '["sample:read", "testlog:read", "report:read", "report:create", "analytics:read"]'),
('访客', '只读权限用户', '["sample:read", "testlog:read"]');
```



#### 3.2.5 报表表 (Reports)
```sql
CREATE TABLE Reports (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ReportName NVARCHAR(200) NOT NULL,         -- 报表名称
    ReportType NVARCHAR(50) NOT NULL,          -- 报表类型
    Parameters NVARCHAR(MAX),                  -- 报表参数JSON
    FilePath NVARCHAR(1000),                   -- 文件路径
    FileFormat NVARCHAR(20),                   -- 文件格式
    FileSize BIGINT,                           -- 文件大小
    Status INT NOT NULL DEFAULT 0,             -- 状态 (0:生成中, 1:已完成, 2:失败)
    Progress INT DEFAULT 0,                    -- 生成进度
    ErrorMessage NVARCHAR(MAX),                -- 错误信息
    GeneratedBy NVARCHAR(100),                 -- 生成人
    GeneratedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    ExpiresAt DATETIME2,                       -- 过期时间

    -- 索引
    INDEX IX_Reports_ReportType (ReportType),
    INDEX IX_Reports_Status (Status),
    INDEX IX_Reports_GeneratedBy (GeneratedBy),
    INDEX IX_Reports_GeneratedAt (GeneratedAt)
);
```

#### 3.2.6 系统配置表 (SystemConfigs)
```sql
CREATE TABLE SystemConfigs (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    ConfigKey NVARCHAR(100) NOT NULL UNIQUE,   -- 配置键
    ConfigValue NVARCHAR(MAX),                 -- 配置值
    ConfigType NVARCHAR(50),                   -- 配置类型
    Description NVARCHAR(500),                 -- 配置描述
    IsEncrypted BIT NOT NULL DEFAULT 0,        -- 是否加密
    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedBy NVARCHAR(100),                   -- 更新人

    -- 索引
    INDEX IX_SystemConfigs_ConfigKey (ConfigKey),
    INDEX IX_SystemConfigs_ConfigType (ConfigType)
);

-- 插入默认配置
INSERT INTO SystemConfigs (ConfigKey, ConfigValue, ConfigType, Description) VALUES
('UID_FORMAT', 'YYYYMMDD-BATCH-NNNN', 'STRING', 'UID生成格式'),
('NAS_BASE_PATH', '\\\\NAS-Server\\TestLogs', 'STRING', 'NAS基础路径'),
('MAX_FILE_SIZE', '104857600', 'INTEGER', '最大文件大小(字节)'),
('REPORT_RETENTION_DAYS', '30', 'INTEGER', '报表保留天数'),
('AUTO_CLEANUP_ENABLED', 'true', 'BOOLEAN', '是否启用自动清理');
```

#### 3.2.7 操作日志表 (AuditLogs)
```sql
CREATE TABLE AuditLogs (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    UserId INT,                                -- 用户ID
    Username NVARCHAR(100),                    -- 用户名
    Action NVARCHAR(100) NOT NULL,             -- 操作动作
    EntityType NVARCHAR(100),                  -- 实体类型
    EntityId NVARCHAR(100),                    -- 实体ID
    OldValues NVARCHAR(MAX),                   -- 旧值JSON
    NewValues NVARCHAR(MAX),                   -- 新值JSON
    IpAddress NVARCHAR(50),                    -- IP地址
    UserAgent NVARCHAR(500),                   -- 用户代理
    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),

    -- 外键约束
    CONSTRAINT FK_AuditLogs_Users FOREIGN KEY (UserId) REFERENCES Users(Id),

    -- 索引
    INDEX IX_AuditLogs_UserId (UserId),
    INDEX IX_AuditLogs_Action (Action),
    INDEX IX_AuditLogs_EntityType (EntityType),
    INDEX IX_AuditLogs_CreatedAt (CreatedAt)
);
```

### 3.3 数据库设计要点
- **索引策略**: UID、BatchNumber、TestTime等关键字段建立索引
- **分区策略**: TestLogs和AuditLogs按时间分区存储历史数据
- **约束设计**: UID唯一约束、外键约束、检查约束
- **性能优化**: 读写分离、连接池配置、查询优化
- **数据归档**: 定期归档历史数据，保持表性能

### 3.4 数据库关系图
```
┌─────────────┐    1:N    ┌─────────────┐
│   Batches   │◄─────────►│  TestLogs   │
│  (批次管理)  │           │ (单片测试)   │
└─────────────┘           └─────────────┘

┌─────────────┐    1:N    ┌─────────────┐
│    Roles    │◄─────────►│    Users    │
└─────────────┘           └─────────────┘
                                 │ 1:N
                                 ▼
                          ┌─────────────┐
                          │ AuditLogs   │
                          └─────────────┘

┌─────────────┐           ┌─────────────┐
│   Reports   │           │SystemConfigs│
└─────────────┘           └─────────────┘
```

### 3.5 存储过程设计

#### 3.5.1 创建批次存储过程
```sql
CREATE PROCEDURE sp_CreateBatch
    @BatchNumber NVARCHAR(50),
    @BatchName NVARCHAR(200) = NULL,
    @Model NVARCHAR(100),
    @PlannedQuantity INT,
    @ExpectedCapacity BIGINT = NULL,
    @Supplier NVARCHAR(200) = NULL,
    @QualityGrade NVARCHAR(20) = NULL,
    @Description NVARCHAR(1000) = NULL,
    @CreatedBy NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRANSACTION;

    TRY
        -- 检查批次号是否已存在
        IF EXISTS (SELECT 1 FROM Batches WHERE BatchNumber = @BatchNumber)
        BEGIN
            RAISERROR('批次号 %s 已存在', 16, 1, @BatchNumber);
            RETURN;
        END

        -- 插入批次记录
        INSERT INTO Batches (
            BatchNumber, BatchName, Model, ProductionDate,
            PlannedQuantity, ExpectedCapacity, Supplier,
            QualityGrade, Description, CreatedBy, UpdatedBy
        )
        VALUES (
            @BatchNumber, @BatchName, @Model, GETDATE(),
            @PlannedQuantity, @ExpectedCapacity, @Supplier,
            @QualityGrade, @Description, @CreatedBy, @CreatedBy
        );

        COMMIT TRANSACTION;

        SELECT 'SUCCESS' AS Result, @BatchNumber AS BatchNumber;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;

        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
```

#### 3.5.2 更新批次统计存储过程
```sql
CREATE PROCEDURE sp_UpdateBatchStatistics
    @BatchNumber NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @TestedQuantity INT;
    DECLARE @PassedQuantity INT;
    DECLARE @FailedQuantity INT;

    -- 统计测试数据
    SELECT
        @TestedQuantity = COUNT(*),
        @PassedQuantity = COUNT(CASE WHEN TestResult = 1 THEN 1 END),
        @FailedQuantity = COUNT(CASE WHEN TestResult = 0 THEN 1 END)
    FROM TestLogs
    WHERE BatchNumber = @BatchNumber;

    -- 更新批次统计
    UPDATE Batches
    SET
        TestedQuantity = @TestedQuantity,
        PassedQuantity = @PassedQuantity,
        FailedQuantity = @FailedQuantity,
        Status = CASE
            WHEN @TestedQuantity = 0 THEN 0  -- 待测试
            WHEN @TestedQuantity < PlannedQuantity THEN 1  -- 测试中
            WHEN @FailedQuantity > 0 THEN 3  -- 测试失败
            ELSE 2  -- 测试完成
        END,
        UpdatedAt = GETDATE()
    WHERE BatchNumber = @BatchNumber;

    -- 返回更新结果
    SELECT
        BatchNumber,
        TestedQuantity,
        PassedQuantity,
        FailedQuantity,
        CAST(PassedQuantity * 100.0 / NULLIF(TestedQuantity, 0) AS DECIMAL(5,2)) AS PassRate
    FROM Batches
    WHERE BatchNumber = @BatchNumber;
END
```

#### 3.5.3 测试统计存储过程
```sql
CREATE PROCEDURE sp_GetTestStatistics
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL,
    @BatchNumber NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    -- 设置默认日期范围
    IF @StartDate IS NULL SET @StartDate = DATEADD(DAY, -30, GETDATE());
    IF @EndDate IS NULL SET @EndDate = GETDATE();

    -- 总体统计
    SELECT
        COUNT(DISTINCT tl.SampleUID) AS TotalSamples,
        COUNT(tl.Id) AS TotalTests,
        COUNT(CASE WHEN tl.TestResult = 1 THEN 1 END) AS PassedTests,
        COUNT(CASE WHEN tl.TestResult = 0 THEN 1 END) AS FailedTests,
        CAST(COUNT(CASE WHEN tl.TestResult = 1 THEN 1 END) * 100.0 / COUNT(tl.Id) AS DECIMAL(5,2)) AS PassRate
    FROM TestLogs tl
    WHERE tl.TestTime BETWEEN @StartDate AND @EndDate
        AND (@BatchNumber IS NULL OR tl.BatchNumber = @BatchNumber);

    -- 按日期统计
    SELECT
        CAST(tl.TestTime AS DATE) AS TestDate,
        COUNT(tl.Id) AS TestCount,
        COUNT(CASE WHEN tl.TestResult = 1 THEN 1 END) AS PassCount,
        COUNT(CASE WHEN tl.TestResult = 0 THEN 1 END) AS FailCount,
        CAST(COUNT(CASE WHEN tl.TestResult = 1 THEN 1 END) * 100.0 / COUNT(tl.Id) AS DECIMAL(5,2)) AS PassRate
    FROM TestLogs tl
    WHERE tl.TestTime BETWEEN @StartDate AND @EndDate
        AND (@BatchNumber IS NULL OR tl.BatchNumber = @BatchNumber)
    GROUP BY CAST(tl.TestTime AS DATE)
    ORDER BY TestDate;

    -- 按批次统计
    SELECT
        b.BatchNumber,
        b.PlannedQuantity,
        b.TestedQuantity,
        b.PassedQuantity,
        b.FailedQuantity,
        CAST(b.PassedQuantity * 100.0 / NULLIF(b.TestedQuantity, 0) AS DECIMAL(5,2)) AS PassRate,
        b.Status
    FROM Batches b
    WHERE (@BatchNumber IS NULL OR b.BatchNumber = @BatchNumber)
        AND EXISTS (
            SELECT 1 FROM TestLogs tl
            WHERE tl.BatchNumber = b.BatchNumber
                AND tl.TestTime BETWEEN @StartDate AND @EndDate
        )
    ORDER BY b.BatchNumber;
END
```

#### 3.5.3 数据清理存储过程
```sql
CREATE PROCEDURE sp_CleanupOldData
    @RetentionDays INT = 90,
    @DryRun BIT = 1
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @CutoffDate DATETIME2 = DATEADD(DAY, -@RetentionDays, GETDATE());
    DECLARE @DeletedReports INT = 0;
    DECLARE @DeletedAuditLogs INT = 0;
    DECLARE @ArchivedTestLogs INT = 0;

    BEGIN TRANSACTION;

    TRY
        -- 清理过期报表
        IF @DryRun = 0
        BEGIN
            DELETE FROM Reports
            WHERE ExpiresAt IS NOT NULL AND ExpiresAt < GETDATE();
            SET @DeletedReports = @@ROWCOUNT;
        END
        ELSE
        BEGIN
            SELECT @DeletedReports = COUNT(*)
            FROM Reports
            WHERE ExpiresAt IS NOT NULL AND ExpiresAt < GETDATE();
        END

        -- 清理旧审计日志
        IF @DryRun = 0
        BEGIN
            DELETE FROM AuditLogs
            WHERE CreatedAt < @CutoffDate;
            SET @DeletedAuditLogs = @@ROWCOUNT;
        END
        ELSE
        BEGIN
            SELECT @DeletedAuditLogs = COUNT(*)
            FROM AuditLogs
            WHERE CreatedAt < @CutoffDate;
        END

        -- 归档旧测试日志 (这里只是标记，实际归档需要额外的归档表)
        IF @DryRun = 0
        BEGIN
            -- 实际实现中，这里应该将数据移动到归档表
            -- UPDATE TestLogs SET IsArchived = 1 WHERE TestTime < @CutoffDate;
            SET @ArchivedTestLogs = 0; -- 暂时设为0
        END
        ELSE
        BEGIN
            SELECT @ArchivedTestLogs = COUNT(*)
            FROM TestLogs
            WHERE TestTime < @CutoffDate;
        END

        IF @DryRun = 0
            COMMIT TRANSACTION;
        ELSE
            ROLLBACK TRANSACTION;

        -- 返回清理结果
        SELECT
            @DeletedReports AS DeletedReports,
            @DeletedAuditLogs AS DeletedAuditLogs,
            @ArchivedTestLogs AS ArchivedTestLogs,
            @DryRun AS DryRun;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;

        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
```

### 3.6 数据库维护

#### 3.6.1 索引维护
```sql
-- 重建索引的存储过程
CREATE PROCEDURE sp_RebuildIndexes
AS
BEGIN
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE index_cursor CURSOR FOR
    SELECT 'ALTER INDEX ' + i.name + ' ON ' + OBJECT_NAME(i.object_id) + ' REBUILD;'
    FROM sys.indexes i
    WHERE i.type > 0
        AND i.is_disabled = 0
        AND OBJECT_NAME(i.object_id) IN ('Samples', 'TestLogs', 'Users', 'AuditLogs');

    OPEN index_cursor;
    FETCH NEXT FROM index_cursor INTO @SQL;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        EXEC sp_executesql @SQL;
        FETCH NEXT FROM index_cursor INTO @SQL;
    END

    CLOSE index_cursor;
    DEALLOCATE index_cursor;
END
```

#### 3.6.2 统计信息更新
```sql
-- 更新统计信息的存储过程
CREATE PROCEDURE sp_UpdateStatistics
AS
BEGIN
    UPDATE STATISTICS Samples;
    UPDATE STATISTICS TestLogs;
    UPDATE STATISTICS Users;
    UPDATE STATISTICS AuditLogs;
    UPDATE STATISTICS Reports;
    UPDATE STATISTICS Batches;
END

### 3.7 数据库备份和恢复策略

#### 3.7.1 备份策略
```sql
-- 完整备份 (每周执行)
BACKUP DATABASE [ProductionTestDB]
TO DISK = 'C:\Backup\ProductionTestDB_Full_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss') + '.bak'
WITH COMPRESSION, CHECKSUM, INIT;

-- 差异备份 (每日执行)
BACKUP DATABASE [ProductionTestDB]
TO DISK = 'C:\Backup\ProductionTestDB_Diff_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss') + '.bak'
WITH DIFFERENTIAL, COMPRESSION, CHECKSUM, INIT;

-- 事务日志备份 (每15分钟执行)
BACKUP LOG [ProductionTestDB]
TO DISK = 'C:\Backup\ProductionTestDB_Log_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss') + '.trn'
WITH COMPRESSION, CHECKSUM, INIT;
```

#### 3.7.2 备份验证
```sql
-- 验证备份文件完整性
RESTORE VERIFYONLY
FROM DISK = 'C:\Backup\ProductionTestDB_Full_20241222_140000.bak';
```

#### 3.7.3 数据库监控
```sql
-- 数据库大小监控
CREATE VIEW v_DatabaseSize AS
SELECT
    DB_NAME() AS DatabaseName,
    SUM(CASE WHEN type = 0 THEN size END) * 8 / 1024 AS DataSizeMB,
    SUM(CASE WHEN type = 1 THEN size END) * 8 / 1024 AS LogSizeMB,
    SUM(size) * 8 / 1024 AS TotalSizeMB
FROM sys.database_files;

-- 表大小监控
CREATE VIEW v_TableSizes AS
SELECT
    t.name AS TableName,
    p.rows AS RowCount,
    SUM(a.total_pages) * 8 / 1024 AS TotalSizeMB,
    SUM(a.used_pages) * 8 / 1024 AS UsedSizeMB,
    (SUM(a.total_pages) - SUM(a.used_pages)) * 8 / 1024 AS UnusedSizeMB
FROM sys.tables t
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE t.name IN ('Samples', 'TestLogs', 'Users', 'AuditLogs', 'Reports', 'Batches')
    AND i.object_id > 255
GROUP BY t.name, p.rows
ORDER BY TotalSizeMB DESC;
```

### 3.8 数据库性能优化

#### 3.8.1 查询优化建议
```sql
-- 1. 使用覆盖索引优化常用查询
CREATE NONCLUSTERED INDEX IX_TestLogs_Covering
ON TestLogs (SampleUID, TestTime, TestResult)
INCLUDE (TestType, TestData, ErrorMessage);

-- 2. 分区表设计 (针对大数据量的TestLogs表)
CREATE PARTITION FUNCTION pf_TestLogsByMonth (DATETIME2)
AS RANGE RIGHT FOR VALUES
('2024-01-01', '2024-02-01', '2024-03-01', '2024-04-01',
 '2024-05-01', '2024-06-01', '2024-07-01', '2024-08-01',
 '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01');

CREATE PARTITION SCHEME ps_TestLogsByMonth
AS PARTITION pf_TestLogsByMonth
ALL TO ([PRIMARY]);

-- 3. 创建分区表
CREATE TABLE TestLogs_Partitioned (
    -- 与原表结构相同
    Id BIGINT IDENTITY(1,1),
    SampleUID NVARCHAR(50) NOT NULL,
    -- ... 其他字段
    TestTime DATETIME2 NOT NULL,
    -- ... 其他字段

    CONSTRAINT PK_TestLogs_Partitioned PRIMARY KEY (Id, TestTime)
) ON ps_TestLogsByMonth(TestTime);
```

#### 3.8.2 性能监控查询
```sql
-- 慢查询监控
SELECT
    qs.execution_count,
    qs.total_elapsed_time / qs.execution_count AS avg_elapsed_time,
    qs.total_logical_reads / qs.execution_count AS avg_logical_reads,
    SUBSTRING(qt.text, (qs.statement_start_offset/2)+1,
        ((CASE qs.statement_end_offset
            WHEN -1 THEN DATALENGTH(qt.text)
            ELSE qs.statement_end_offset
        END - qs.statement_start_offset)/2)+1) AS statement_text
FROM sys.dm_exec_query_stats qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) qt
WHERE qs.total_elapsed_time / qs.execution_count > 1000000 -- 超过1秒的查询
ORDER BY avg_elapsed_time DESC;

-- 索引使用情况监控
SELECT
    i.name AS IndexName,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates,
    s.last_user_seek,
    s.last_user_scan,
    s.last_user_lookup
FROM sys.dm_db_index_usage_stats s
INNER JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
WHERE s.database_id = DB_ID()
    AND OBJECT_NAME(s.object_id) IN ('Samples', 'TestLogs', 'Users')
ORDER BY s.user_seeks + s.user_scans + s.user_lookups DESC;
```
```

## 4. 接口规范

### 4.1 RESTful API设计原则
- **HTTP方法**: GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- **状态码**: 200(成功)、201(创建)、400(请求错误)、401(未授权)、404(未找到)、500(服务器错误)
- **响应格式**: 统一JSON格式

### 4.2 统一响应格式
```csharp
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public T Data { get; set; }
    public int Code { get; set; }
    public DateTime Timestamp { get; set; }
}
```

### 4.3 分页响应格式
```csharp
public class PagedResponse<T>
{
    public List<T> Items { get; set; }
    public int TotalCount { get; set; }
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}
```

## 5. 安全与认证

### 5.1 身份认证
- **JWT Token**: 基于JWT的无状态认证
- **Token刷新**: 支持Token自动刷新机制
- **多端登录**: 支持Web端和桌面端同时登录

### 5.2 权限控制
- **基于角色**: 管理员、测试工程师、质量工程师、访客
- **API权限**: 基于角色的API访问控制
- **数据权限**: 基于用户的数据访问范围控制

### 5.3 安全措施
- **HTTPS**: 强制HTTPS通信
- **CORS**: 跨域请求控制
- **限流**: API请求频率限制
- **日志审计**: 关键操作日志记录

## 6. 性能与监控

### 6.1 性能要求
- **响应时间**: API响应时间 < 500ms
- **并发处理**: 支持100个并发请求
- **数据处理**: 单次处理1000条记录
- **文件上传**: 支持GB级文件上传

### 6.2 缓存策略
- **Redis缓存**: 热点数据缓存
- **查询缓存**: 复杂查询结果缓存
- **文件缓存**: 报表文件临时缓存

### 6.3 监控指标
- **系统监控**: CPU、内存、磁盘使用率
- **应用监控**: API响应时间、错误率
- **业务监控**: 上传成功率、处理速度

## 7. 部署与运维

### 7.1 部署环境
- **容器化**: Docker部署
- **负载均衡**: 支持多实例部署
- **数据库**: SQL Server集群部署
- **缓存**: Redis集群部署

### 7.2 配置管理
- **环境配置**: 开发、测试、生产环境配置
- **连接字符串**: 数据库、Redis、NAS连接配置
- **业务配置**: UID生成规则、文件存储路径等

### 7.3 日志管理
- **结构化日志**: 使用Serilog结构化日志
- **日志级别**: Debug、Info、Warning、Error、Fatal
- **日志存储**: 文件日志 + 数据库日志
- **日志分析**: 支持日志查询和分析

## 8. 开发规范

### 8.1 代码规范
- **命名规范**: Pascal命名法、驼峰命名法
- **注释规范**: XML文档注释
- **异常处理**: 统一异常处理机制
- **单元测试**: 核心业务逻辑单元测试覆盖率 > 80%

### 8.2 开发工具
- **IDE**: Visual Studio 2022
- **版本控制**: Git
- **包管理**: NuGet
- **API文档**: Swagger/OpenAPI

### 8.3 质量保证
- **代码审查**: Pull Request代码审查
- **自动化测试**: 单元测试、集成测试
- **性能测试**: API性能测试
- **安全测试**: 安全漏洞扫描
