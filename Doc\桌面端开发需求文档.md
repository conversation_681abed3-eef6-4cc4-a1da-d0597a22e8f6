# SD卡/U盘开卡测试管理系统 - 桌面端开发需求文档

## 1. 项目概述

### 1.1 系统定位
桌面端应用作为量产工具和Web系统之间的数据中转工具，负责接收量产工具的测试数据并自动上传到管理系统。

### 1.2 工作流程
1. **同时启动**: 量产时与量产工具同时启动
2. **建立连接**: 与量产工具建立通信连接
3. **数据接收**: 量产结束后接收测试结果和日志
4. **数据上传**: 自动上传数据到Web系统和NAS存储
5. **状态反馈**: 向量产工具反馈处理状态

### 1.3 技术架构
- **框架**: WPF (.NET 8.0)
- **UI库**: MaterialDesignInXamlToolkit 或 ModernWpf
- **通信**: System.Net.Sockets (TCP)、System.IO.Pipes (命名管道)
- **HTTP客户端**: HttpClient + Polly (重试策略)
- **序列化**: System.Text.Json
- **日志**: Serilog
- **配置**: Microsoft.Extensions.Configuration
- **自动更新**: Squirrel.Windows

## 2. 核心功能需求

### 2.1 量产工具通信模块

#### 2.1.1 通信协议支持
```csharp
public enum CommunicationProtocol
{
    TcpSocket,      // TCP Socket通信
    NamedPipe,      // 命名管道
    SharedMemory,   // 共享内存 (可选)
    FileShare       // 文件共享监控
}
```

#### 2.1.2 连接管理
- **服务发现**: 自动发现本机运行的量产工具
- **连接建立**: 主动连接或被动监听
- **心跳检测**: 定期检查连接状态
- **断线重连**: 自动重连机制
- **多实例支持**: 同时连接多个量产工具实例

#### 2.1.3 数据接收服务
```csharp
public class DataReceiveService
{
    // 启动监听服务
    Task StartListeningAsync(CommunicationProtocol protocol, int port);
    
    // 接收数据事件
    event EventHandler<DataReceivedEventArgs> DataReceived;
    
    // 发送确认消息
    Task SendAcknowledgmentAsync(string messageId, bool success);
    
    // 停止服务
    Task StopAsync();
}
```

### 2.2 数据处理模块

#### 2.2.1 数据格式定义
```csharp
public class ProductionData
{
    public string MessageId { get; set; }        // 消息ID
    public string UID { get; set; }              // 样品UID
    public DateTime TestTime { get; set; }       // 测试时间
    public string TestType { get; set; }         // 测试类型
    public bool TestResult { get; set; }         // 测试结果
    public Dictionary<string, object> TestData { get; set; } // 测试数据
    public List<LogFile> LogFiles { get; set; }  // 日志文件列表
    public string DeviceInfo { get; set; }       // 设备信息
}

public class LogFile
{
    public string FileName { get; set; }
    public byte[] FileContent { get; set; }
    public string FileType { get; set; }
    public long FileSize { get; set; }
}
```

#### 2.2.2 数据验证服务
- **格式验证**: 验证接收数据的格式正确性
- **完整性检查**: 检查数据的完整性和一致性
- **UID验证**: 验证UID的有效性和唯一性
- **文件验证**: 验证日志文件的完整性

#### 2.2.3 数据转换服务
- **格式标准化**: 将量产工具数据转换为系统标准格式
- **数据映射**: 字段映射和数据类型转换
- **文件处理**: 日志文件的重命名和路径处理

### 2.3 系统上传模块

#### 2.3.1 Web API集成
```csharp
public class WebApiService
{
    // 上传样品数据
    Task<bool> UploadSampleDataAsync(SampleData data);
    
    // 上传测试日志
    Task<bool> UploadTestLogAsync(TestLogData log);
    
    // 批量上传
    Task<BatchUploadResult> BatchUploadAsync(List<ProductionData> dataList);
    
    // 获取上传状态
    Task<UploadStatus> GetUploadStatusAsync(string uploadId);
}
```

#### 2.3.2 NAS存储集成
```csharp
public class NasStorageService
{
    // 上传文件到NAS
    Task<string> UploadFileAsync(string localPath, string remotePath);
    
    // 批量上传文件
    Task<List<string>> BatchUploadFilesAsync(Dictionary<string, string> filePaths);
    
    // 验证NAS连接
    Task<bool> TestConnectionAsync();
    
    // 获取存储空间信息
    Task<StorageInfo> GetStorageInfoAsync();
}
```

#### 2.3.3 上传队列管理
- **队列机制**: 使用队列管理上传任务
- **优先级**: 支持任务优先级设置
- **重试机制**: 失败任务自动重试
- **并发控制**: 控制同时上传的任务数量

### 2.4 用户界面模块

#### 2.4.1 主界面设计
```xml
<Window x:Class="ProductionDataTransfer.MainWindow">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>    <!-- 工具栏 -->
            <RowDefinition Height="*"/>       <!-- 主内容区 -->
            <RowDefinition Height="Auto"/>    <!-- 状态栏 -->
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <ToolBar Grid.Row="0">
            <Button Name="StartButton" Content="启动监听"/>
            <Button Name="StopButton" Content="停止监听"/>
            <Button Name="SettingsButton" Content="设置"/>
        </ToolBar>
        
        <!-- 主内容区 -->
        <TabControl Grid.Row="1">
            <TabItem Header="连接状态">
                <!-- 连接状态显示 -->
            </TabItem>
            <TabItem Header="数据队列">
                <!-- 接收和处理队列 -->
            </TabItem>
            <TabItem Header="上传进度">
                <!-- 上传进度显示 -->
            </TabItem>
            <TabItem Header="操作日志">
                <!-- 日志显示 -->
            </TabItem>
        </TabControl>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem Content="就绪"/>
            <StatusBarItem Content="连接状态: 未连接"/>
        </StatusBar>
    </Grid>
</Window>
```

#### 2.4.2 连接状态页面
- **量产工具连接**: 显示已连接的量产工具列表
- **连接参数**: IP地址、端口、协议类型
- **连接状态**: 在线/离线状态指示
- **数据统计**: 接收数据量、成功率等统计信息

#### 2.4.3 数据队列页面
- **接收队列**: 显示从量产工具接收的数据
- **处理队列**: 显示正在处理的数据
- **上传队列**: 显示等待上传的数据
- **历史记录**: 已处理完成的数据记录

#### 2.4.4 上传进度页面
- **当前任务**: 正在上传的任务进度
- **队列状态**: 等待上传的任务列表
- **成功记录**: 上传成功的任务记录
- **失败记录**: 上传失败的任务和错误信息

### 2.5 配置管理模块

#### 2.5.1 通信配置
```csharp
public class CommunicationConfig
{
    public CommunicationProtocol Protocol { get; set; }
    public string IpAddress { get; set; }
    public int Port { get; set; }
    public int TimeoutSeconds { get; set; }
    public int RetryCount { get; set; }
    public bool AutoReconnect { get; set; }
}
```

#### 2.5.2 系统配置
```csharp
public class SystemConfig
{
    public string WebApiBaseUrl { get; set; }
    public string ApiKey { get; set; }
    public string NasServerPath { get; set; }
    public string NasUsername { get; set; }
    public string NasPassword { get; set; }
    public int MaxConcurrentUploads { get; set; }
    public int MaxRetryCount { get; set; }
}
```

#### 2.5.3 配置界面
- **通信设置**: 协议选择、端口配置、超时设置
- **服务器设置**: Web API地址、认证信息
- **NAS设置**: NAS服务器地址、认证信息、存储路径
- **高级设置**: 并发数、重试次数、日志级别

### 2.6 日志与监控模块

#### 2.6.1 日志记录
```csharp
public class LoggingService
{
    // 记录连接日志
    void LogConnection(string message, LogLevel level);
    
    // 记录数据处理日志
    void LogDataProcessing(string uid, string message, LogLevel level);
    
    // 记录上传日志
    void LogUpload(string uid, string message, LogLevel level);
    
    // 记录错误日志
    void LogError(Exception ex, string context);
}
```

#### 2.6.2 性能监控
- **内存使用**: 监控应用内存使用情况
- **CPU使用**: 监控CPU使用率
- **网络流量**: 监控网络数据传输量
- **处理速度**: 监控数据处理和上传速度

#### 2.6.3 异常处理
- **全局异常**: 全局异常捕获和处理
- **网络异常**: 网络连接异常的处理
- **文件异常**: 文件操作异常的处理
- **用户通知**: 异常情况的用户友好提示

## 3. 技术实现要求

### 3.1 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   量产工具      │◄──►│   桌面端应用    │◄──►│   Web系统       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   NAS存储       │
                       └─────────────────┘
```

### 3.2 多线程设计
- **UI线程**: 界面更新和用户交互
- **通信线程**: 与量产工具的通信处理
- **处理线程**: 数据处理和转换
- **上传线程**: 数据上传到系统
- **监控线程**: 系统监控和日志记录

### 3.3 数据流设计
```csharp
量产工具数据 → 接收队列 → 数据验证 → 格式转换 → 上传队列 → 系统上传 → 完成确认
```

### 3.4 错误处理策略
- **重试机制**: 指数退避重试策略
- **降级处理**: 网络异常时的本地缓存
- **用户提示**: 友好的错误提示和解决建议
- **日志记录**: 详细的错误日志记录

## 4. 部署与安装

### 4.1 安装包制作
- **MSI安装包**: 使用WiX Toolset制作
- **依赖检测**: 自动检测和安装.NET Runtime
- **静默安装**: 支持企业环境批量部署
- **卸载程序**: 完整的卸载功能

### 4.2 自动更新
```csharp
public class UpdateService
{
    // 检查更新
    Task<UpdateInfo> CheckForUpdatesAsync();
    
    // 下载更新
    Task<bool> DownloadUpdateAsync(UpdateInfo update, IProgress<int> progress);
    
    // 应用更新
    Task<bool> ApplyUpdateAsync();
    
    // 回滚更新
    Task<bool> RollbackUpdateAsync();
}
```

### 4.3 系统要求
- **操作系统**: Windows 10/11 (x64)
- **运行时**: .NET 8.0 Desktop Runtime
- **内存**: 最低2GB，推荐4GB+
- **磁盘**: 最低100MB可用空间
- **网络**: 能访问Web API和NAS服务器

## 5. 测试要求

### 5.1 单元测试
- **业务逻辑**: 数据处理、验证逻辑的单元测试
- **通信模块**: 网络通信功能的模拟测试
- **配置管理**: 配置读取和验证的测试
- **覆盖率**: 核心业务逻辑覆盖率 > 80%

### 5.2 集成测试
- **量产工具集成**: 与模拟量产工具的集成测试
- **Web API集成**: 与后端API的集成测试
- **NAS集成**: 与NAS存储的集成测试
- **端到端测试**: 完整数据流的端到端测试

### 5.3 性能测试
- **并发处理**: 多个量产工具同时连接的性能测试
- **大数据量**: 大量数据处理的性能测试
- **长时间运行**: 长时间运行的稳定性测试
- **内存泄漏**: 内存使用情况的监控测试

## 6. 运维与监控

### 6.1 日志管理
- **日志级别**: Debug、Info、Warning、Error、Fatal
- **日志轮转**: 按大小和时间自动轮转日志文件
- **日志上传**: 支持将日志上传到中央日志系统
- **日志分析**: 提供日志查看和分析工具

### 6.2 监控指标
- **连接状态**: 量产工具连接状态监控
- **处理性能**: 数据处理速度和成功率
- **上传状态**: 数据上传成功率和失败原因
- **系统资源**: CPU、内存、磁盘使用情况

### 6.3 故障处理
- **自动恢复**: 常见故障的自动恢复机制
- **故障报告**: 自动生成故障报告
- **远程诊断**: 支持远程诊断和问题排查
- **紧急处理**: 紧急情况下的手动干预机制

## 7. 安全要求

### 7.1 通信安全
- **数据加密**: 敏感数据传输加密
- **身份验证**: 与Web系统的身份验证
- **访问控制**: 基于角色的功能访问控制
- **审计日志**: 关键操作的审计日志

### 7.2 数据安全
- **本地加密**: 本地缓存数据加密存储
- **传输安全**: HTTPS/TLS加密传输
- **数据完整性**: 数据传输完整性校验
- **隐私保护**: 敏感信息的脱敏处理

## 8. 开发规范

### 8.1 代码规范
- **命名规范**: C#命名约定
- **注释规范**: XML文档注释
- **异常处理**: 统一的异常处理机制
- **代码审查**: Pull Request代码审查

### 8.2 版本管理
- **版本号**: 语义化版本号 (Major.Minor.Patch)
- **发布说明**: 详细的版本更新说明
- **兼容性**: 向后兼容性保证
- **升级路径**: 平滑的版本升级路径
