# SD卡/U盘开卡测试管理系统 - 量产工具通讯接口规范

## 1. 接口概述

### 1.1 通讯目标
量产工具需要与桌面端数据中转工具建立通信连接，在测试完成后将测试结果和日志数据发送给桌面端，由桌面端负责上传到管理系统。

### 1.2 通讯架构
```
┌─────────────────┐    通讯协议    ┌─────────────────┐
│   量产工具      │◄─────────────►│   桌面端应用    │
│                 │    数据传输    │                 │
└─────────────────┘               └─────────────────┘
```

### 1.3 支持的通讯协议
- **Boost共享内存**: 主要协议，高性能本机进程间通信
- **命名管道**: 备用协议，兼容性好
- **TCP Socket**: 备用协议，支持网络通信
- **文件共享**: 兜底方案，通过监控共享文件夹进行数据交换

## 2. Boost共享内存通讯协议 (主要协议)

### 2.1 共享内存架构设计

#### 2.1.1 内存布局
```cpp
// 共享内存总体布局
struct SharedMemoryLayout {
    ControlBlock control;           // 控制块 (1KB)
    MessageQueue messageQueue;      // 消息队列 (4KB)
    DataBuffer dataBuffer;          // 数据缓冲区 (可配置大小，默认10MB)
    LogBuffer logBuffer;            // 日志缓冲区 (1MB)
};

// 控制块结构
struct ControlBlock {
    uint32_t magic;                 // 魔数: 0x12345678
    uint32_t version;               // 协议版本
    uint32_t producerPid;           // 量产工具进程ID
    uint32_t consumerPid;           // 桌面端进程ID
    volatile uint32_t producerAlive; // 量产工具存活标志
    volatile uint32_t consumerAlive; // 桌面端存活标志
    uint64_t totalDataSize;         // 数据缓冲区总大小
    uint64_t messageQueueSize;      // 消息队列大小
    char reserved[960];             // 保留字段
};
```

#### 2.1.2 共享内存配置
- **共享内存名称**: `ProductionDataTransfer_SharedMemory`
- **默认大小**: 16MB (可配置)
- **访问权限**: 读写权限
- **同步机制**: 命名信号量 + 原子操作

### 2.2 同步机制

#### 2.2.1 信号量定义
```cpp
// 信号量名称定义
#define SEM_PRODUCER_READY    "ProductionTool_Producer_Ready"
#define SEM_CONSUMER_READY    "ProductionTool_Consumer_Ready"
#define SEM_DATA_AVAILABLE    "ProductionTool_Data_Available"
#define SEM_DATA_PROCESSED    "ProductionTool_Data_Processed"
#define SEM_MUTEX             "ProductionTool_Mutex"

// 使用Boost信号量
boost::interprocess::named_semaphore producer_ready(
    boost::interprocess::open_or_create,
    SEM_PRODUCER_READY,
    0
);
```

#### 2.2.2 消息队列结构
```cpp
// 循环消息队列
struct MessageQueue {
    volatile uint32_t head;         // 队列头指针
    volatile uint32_t tail;         // 队列尾指针
    uint32_t capacity;              // 队列容量
    uint32_t messageSize;           // 单个消息大小
    char messages[0];               // 消息数据区
};

// 消息结构
struct Message {
    uint32_t messageType;           // 消息类型
    uint32_t messageId;             // 消息ID
    uint64_t timestamp;             // 时间戳
    uint32_t dataOffset;            // 数据在缓冲区中的偏移
    uint32_t dataSize;              // 数据大小
    uint32_t checksum;              // 校验和
    char reserved[36];              // 保留字段，总共64字节
};
```

### 2.3 初始化和连接建立

#### 2.3.1 桌面端初始化 (Consumer)
```cpp
class SharedMemoryConsumer {
private:
    boost::interprocess::managed_shared_memory segment;
    SharedMemoryLayout* layout;
    boost::interprocess::named_semaphore producer_ready;
    boost::interprocess::named_semaphore consumer_ready;

public:
    bool Initialize() {
        try {
            // 创建共享内存段
            segment = boost::interprocess::managed_shared_memory(
                boost::interprocess::create_only,
                "ProductionDataTransfer_SharedMemory",
                16 * 1024 * 1024  // 16MB
            );

            // 分配内存布局
            layout = segment.construct<SharedMemoryLayout>("Layout")();

            // 初始化控制块
            layout->control.magic = 0x12345678;
            layout->control.version = 1;
            layout->control.consumerPid = GetCurrentProcessId();
            layout->control.consumerAlive = 1;

            // 创建信号量
            producer_ready = boost::interprocess::named_semaphore(
                boost::interprocess::create_only, SEM_PRODUCER_READY, 0);
            consumer_ready = boost::interprocess::named_semaphore(
                boost::interprocess::create_only, SEM_CONSUMER_READY, 1);

            return true;
        } catch (const std::exception& e) {
            return false;
        }
    }
};
```

#### 2.3.2 量产工具连接 (Producer)
```cpp
class SharedMemoryProducer {
private:
    boost::interprocess::managed_shared_memory segment;
    SharedMemoryLayout* layout;

public:
    bool Connect() {
        try {
            // 打开现有共享内存段
            segment = boost::interprocess::managed_shared_memory(
                boost::interprocess::open_only,
                "ProductionDataTransfer_SharedMemory"
            );

            // 获取内存布局
            auto result = segment.find<SharedMemoryLayout>("Layout");
            if (result.first == nullptr) return false;
            layout = result.first;

            // 验证魔数和版本
            if (layout->control.magic != 0x12345678 ||
                layout->control.version != 1) {
                return false;
            }

            // 设置生产者信息
            layout->control.producerPid = GetCurrentProcessId();
            layout->control.producerAlive = 1;

            // 通知桌面端生产者已就绪
            boost::interprocess::named_semaphore producer_ready(
                boost::interprocess::open_only, SEM_PRODUCER_READY);
            producer_ready.post();

            return true;
        } catch (const std::exception& e) {
            return false;
        }
    }
};
```

## 3. 共享内存数据传输协议

### 3.1 数据传输流程

#### 3.1.1 量产工具发送数据流程
```cpp
bool SharedMemoryProducer::SendTestData(const TestData& testData) {
    // 1. 获取互斥锁
    boost::interprocess::named_semaphore mutex(
        boost::interprocess::open_only, SEM_MUTEX);
    mutex.wait();

    // 2. 序列化测试数据
    std::string jsonData = SerializeTestData(testData);

    // 3. 检查缓冲区空间
    if (jsonData.size() > layout->control.totalDataSize) {
        mutex.post();
        return false; // 数据太大
    }

    // 4. 写入数据到缓冲区
    uint32_t dataOffset = AllocateDataBuffer(jsonData.size());
    memcpy(layout->dataBuffer.data + dataOffset,
           jsonData.c_str(), jsonData.size());

    // 5. 创建消息
    Message msg;
    msg.messageType = MSG_DATA_TRANSFER;
    msg.messageId = GenerateMessageId();
    msg.timestamp = GetCurrentTimestamp();
    msg.dataOffset = dataOffset;
    msg.dataSize = jsonData.size();
    msg.checksum = CalculateChecksum(jsonData);

    // 6. 将消息放入队列
    if (!PushMessage(msg)) {
        FreeDataBuffer(dataOffset, jsonData.size());
        mutex.post();
        return false;
    }

    // 7. 释放互斥锁并通知桌面端
    mutex.post();
    boost::interprocess::named_semaphore data_available(
        boost::interprocess::open_only, SEM_DATA_AVAILABLE);
    data_available.post();

    return true;
}
```

#### 3.1.2 桌面端接收数据流程
```cpp
bool SharedMemoryConsumer::ReceiveTestData() {
    // 1. 等待数据可用信号
    boost::interprocess::named_semaphore data_available(
        boost::interprocess::open_only, SEM_DATA_AVAILABLE);
    data_available.wait();

    // 2. 获取互斥锁
    boost::interprocess::named_semaphore mutex(
        boost::interprocess::open_only, SEM_MUTEX);
    mutex.wait();

    // 3. 从消息队列读取消息
    Message msg;
    if (!PopMessage(msg)) {
        mutex.post();
        return false;
    }

    // 4. 验证消息
    if (msg.messageType != MSG_DATA_TRANSFER) {
        mutex.post();
        return false;
    }

    // 5. 读取数据
    std::string jsonData(layout->dataBuffer.data + msg.dataOffset,
                        msg.dataSize);

    // 6. 验证校验和
    if (CalculateChecksum(jsonData) != msg.checksum) {
        FreeDataBuffer(msg.dataOffset, msg.dataSize);
        mutex.post();
        return false;
    }

    // 7. 释放数据缓冲区
    FreeDataBuffer(msg.dataOffset, msg.dataSize);
    mutex.post();

    // 8. 处理数据
    ProcessTestData(jsonData);

    // 9. 发送处理完成信号
    boost::interprocess::named_semaphore data_processed(
        boost::interprocess::open_only, SEM_DATA_PROCESSED);
    data_processed.post();

    return true;
}
```

### 3.2 消息类型定义
```cpp
enum MessageType : uint32_t {
    MSG_HANDSHAKE_REQUEST = 0x0001,     // 握手请求
    MSG_HANDSHAKE_RESPONSE = 0x0002,    // 握手响应
    MSG_HEARTBEAT = 0x0003,             // 心跳包
    MSG_DATA_TRANSFER = 0x0004,         // 数据传输
    MSG_DATA_ACK = 0x0005,              // 数据确认
    MSG_ERROR = 0x0006,                 // 错误消息
    MSG_DISCONNECT = 0x0007             // 断开连接
};
```

### 3.3 测试数据格式
```json
{
  "messageType": "DATA_TRANSFER",
  "messageId": "uuid-string",
  "timestamp": "2024-12-22T14:30:22.123Z",
  "batchId": "BATCH_20241222_001",
  "testData": {
    "uid": "20241222-A001-0001",
    "testType": "FULL_TEST",
    "testStartTime": "2024-12-22T14:25:00.000Z",
    "testEndTime": "2024-12-22T14:30:00.000Z",
    "testResult": "PASS",
    "deviceInfo": {
      "stationId": "STATION_001",
      "operatorId": "OP001",
      "softwareVersion": "1.0.0"
    },
    "testResults": {
      "capacityTest": {
        "result": "PASS",
        "expectedCapacity": 32000000000,
        "actualCapacity": 31999234567,
        "tolerance": 0.1
      },
      "speedTest": {
        "result": "PASS",
        "readSpeed": 95.5,
        "writeSpeed": 87.3,
        "unit": "MB/s"
      },
      "badBlockTest": {
        "result": "PASS",
        "badBlockCount": 0,
        "maxAllowedBadBlocks": 10
      }
    },
    "logFiles": [
      {
        "fileName": "20241222-A001-0001_FullTest_20241222143022.log",
        "fileSize": 1024567,
        "fileType": "LOG",
        "encoding": "UTF-8",
        "content": "base64-encoded-content"
      },
      {
        "fileName": "20241222-A001-0001_FullTest_20241222143022.csv",
        "fileSize": 2048,
        "fileType": "CSV",
        "encoding": "UTF-8",
        "content": "base64-encoded-content"
      }
    ]
  }
}
```

### 3.4 数据确认机制
```json
// 桌面端发送确认消息
{
  "messageType": "DATA_ACK",
  "messageId": "original-message-id",
  "timestamp": "2024-12-22T14:30:25.123Z",
  "status": "SUCCESS",
  "processedItems": {
    "uid": "20241222-A001-0001",
    "dataReceived": true,
    "dataValidated": true,
    "uploadStarted": true
  },
  "errors": []
}

// 如果有错误
{
  "messageType": "DATA_ACK",
  "messageId": "original-message-id",
  "timestamp": "2024-12-22T14:30:25.123Z",
  "status": "ERROR",
  "errors": [
    {
      "errorCode": "INVALID_UID",
      "errorMessage": "UID格式不正确",
      "field": "testData.uid"
    }
  ]
}
```

## 4. 命名管道通讯协议

### 4.1 管道配置
- **管道名称**: `\\.\pipe\ProductionDataTransfer`
- **访问模式**: 双向通信
- **缓冲区大小**: 64KB
- **超时时间**: 30秒

### 4.2 连接建立
```csharp
// 桌面端创建命名管道服务器
var pipeServer = new NamedPipeServerStream(
    "ProductionDataTransfer",
    PipeDirection.InOut,
    maxNumberOfServerInstances: 10,
    PipeTransmissionMode.Message,
    PipeOptions.Asynchronous
);

// 量产工具连接到命名管道
var pipeClient = new NamedPipeClientStream(
    ".",
    "ProductionDataTransfer",
    PipeDirection.InOut,
    PipeOptions.Asynchronous
);
```

### 4.3 数据格式
命名管道使用与TCP Socket相同的消息格式和协议。

## 5. 文件共享通讯协议

### 5.1 共享目录结构
```
SharedFolder/
├── Input/          # 量产工具写入数据
│   ├── pending/    # 待处理文件
│   └── processing/ # 正在处理文件
├── Output/         # 桌面端写入响应
│   ├── success/    # 处理成功
│   └── error/      # 处理失败
└── Config/         # 配置文件
    └── settings.json
```

### 5.2 文件命名规范
```
输入文件: {UID}_{Timestamp}_{MessageId}.json
输出文件: {UID}_{Timestamp}_{MessageId}_response.json
```

### 5.3 数据文件格式
输入文件内容与TCP协议的JSON格式相同，输出文件为确认响应格式。

## 6. 错误处理

### 6.1 错误代码定义
```csharp
public enum ErrorCode
{
    SUCCESS = 0,
    INVALID_MESSAGE_FORMAT = 1001,
    INVALID_UID = 1002,
    INVALID_TEST_DATA = 1003,
    FILE_TOO_LARGE = 1004,
    UNSUPPORTED_FILE_TYPE = 1005,
    NETWORK_ERROR = 2001,
    SERVER_BUSY = 2002,
    AUTHENTICATION_FAILED = 2003,
    PERMISSION_DENIED = 2004,
    INTERNAL_ERROR = 5001
}
```

### 6.2 错误消息格式
```json
{
  "messageType": "ERROR",
  "messageId": "uuid-string",
  "timestamp": "2024-12-22T14:30:22.123Z",
  "errorCode": 1002,
  "errorMessage": "UID格式不正确",
  "details": {
    "field": "testData.uid",
    "value": "invalid-uid",
    "expectedFormat": "YYYYMMDD-BATCH-NNNN"
  }
}
```

## 7. 实现示例

### 7.1 量产工具端实现示例 (C++)
```cpp
class ProductionToolClient {
private:
    SOCKET clientSocket;
    std::string serverIP;
    int serverPort;
    
public:
    bool Connect(const std::string& ip, int port);
    bool SendHandshake();
    bool SendTestData(const TestData& data);
    bool ReceiveAcknowledgment();
    void Disconnect();
};

// 使用示例
ProductionToolClient client;
if (client.Connect("127.0.0.1", 8888)) {
    if (client.SendHandshake()) {
        TestData data = CollectTestData();
        if (client.SendTestData(data)) {
            client.ReceiveAcknowledgment();
        }
    }
    client.Disconnect();
}
```

### 7.2 量产工具端实现示例 (C#)
```csharp
public class ProductionToolClient
{
    private TcpClient tcpClient;
    private NetworkStream stream;
    
    public async Task<bool> ConnectAsync(string serverIP, int port)
    {
        try
        {
            tcpClient = new TcpClient();
            await tcpClient.ConnectAsync(serverIP, port);
            stream = tcpClient.GetStream();
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接失败: {ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> SendTestDataAsync(TestData testData)
    {
        try
        {
            var message = CreateDataMessage(testData);
            var jsonData = JsonSerializer.Serialize(message);
            var buffer = Encoding.UTF8.GetBytes(jsonData);
            
            await stream.WriteAsync(buffer, 0, buffer.Length);
            
            // 等待确认
            var response = await ReceiveResponseAsync();
            return response.Status == "SUCCESS";
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发送数据失败: {ex.Message}");
            return false;
        }
    }
}
```

### 7.3 Python实现示例
```python
import socket
import json
import uuid
from datetime import datetime

class ProductionToolClient:
    def __init__(self):
        self.socket = None
        
    def connect(self, server_ip, server_port):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((server_ip, server_port))
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def send_test_data(self, test_data):
        try:
            message = {
                "messageType": "DATA_TRANSFER",
                "messageId": str(uuid.uuid4()),
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "testData": test_data
            }
            
            json_data = json.dumps(message)
            self.socket.send(json_data.encode('utf-8'))
            
            # 接收确认
            response = self.socket.recv(4096)
            response_data = json.loads(response.decode('utf-8'))
            
            return response_data.get('status') == 'SUCCESS'
        except Exception as e:
            print(f"发送数据失败: {e}")
            return False
    
    def disconnect(self):
        if self.socket:
            self.socket.close()
```

## 8. 测试与验证

### 8.1 连接测试
- 验证TCP连接建立和断开
- 验证命名管道连接
- 验证文件共享监控

### 8.2 数据传输测试
- 小数据量传输测试
- 大数据量传输测试
- 并发传输测试
- 网络异常恢复测试

### 8.3 协议兼容性测试
- 不同版本协议兼容性
- 错误数据格式处理
- 异常情况处理

## 9. 部署配置

### 9.1 网络配置
- 确保防火墙开放通信端口
- 配置网络访问权限
- 设置网络超时参数

### 9.2 安全配置
- 配置访问控制列表
- 设置数据传输加密 (可选)
- 配置审计日志

### 9.3 性能调优
- 调整缓冲区大小
- 设置合适的超时时间
- 配置并发连接数限制

## 10. 故障排除

### 10.1 常见问题
- 连接超时: 检查网络连接和防火墙设置
- 数据格式错误: 验证JSON格式和字段完整性
- 文件传输失败: 检查文件大小和编码格式

### 10.2 调试工具
- 网络抓包工具 (Wireshark)
- TCP连接测试工具 (Telnet)
- JSON格式验证工具

### 10.3 日志分析
- 连接日志分析
- 数据传输日志分析
- 错误日志分析
