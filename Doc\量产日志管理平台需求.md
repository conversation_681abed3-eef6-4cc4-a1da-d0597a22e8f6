# SD卡/U盘开卡测试管理系统需求文档

## 1. 项目背景

### 1.1 现状分析
- 目前SD卡/U盘开卡测试阶段采用人工手动记录测试结果
- 测试日志保存方式不统一，管理困难
- 测试样品编号与日志对应关系维护繁琐，容易出错
- 缺乏统一的数据管理和查询平台
- 量产阶段需要更高效的数据上传和管理方式

### 1.2 项目目标
- 建立统一的测试数据管理平台
- 实现测试样品与日志的自动化关联
- 通过UID机制确保数据的唯一性和可追溯性
- 提供便捷的数据查询和分析功能
- 支持量产阶段的批量数据处理

## 2. 系统概述

### 2.1 系统定位
SD卡/U盘开卡测试管理系统是一个集测试数据采集、存储、管理、查询于一体的综合性平台，旨在提高测试效率，降低人工错误率，实现测试过程的数字化管理。

### 2.2 核心功能
- 样品信息管理
- 测试日志自动上传
- UID生成与写入
- 数据查询与分析
- 报表生成
- 用户权限管理

## 3. 功能需求

### 3.1 样品管理模块

#### 3.1.1 样品信息录入
- **功能描述**: 支持批量或单个录入样品基本信息
- **输入信息**:
  - 样品批次号
  - 样品型号
  - 生产日期
  - 供应商信息
  - 预期容量
  - 其他自定义属性
- **输出**: 系统自动生成唯一UID

#### 3.1.2 UID管理
- **UID生成规则**:
  - 格式: YYYYMMDD-BATCH-NNNN (年月日-批次-序号)
  - 示例: 20241222-A001-0001
- **UID写入**: 支持将UID写入样品存储区域
- **UID验证**: 确保UID的唯一性和有效性

#### 3.1.3 样品状态跟踪
- 待测试
- 测试中
- 测试完成
- 测试失败
- 已出货

### 3.2 测试日志管理模块

#### 3.2.1 日志自动上传与存储
- **支持格式**: TXT, LOG, CSV, JSON
- **上传方式**:
  - 手动上传到系统，自动转存NAS
  - 自动监控文件夹上传
  - API接口上传
  - 直接从测试设备推送到NAS
- **存储架构**:
  - **主存储**: 专用NAS存储系统
  - **本地缓存**: 系统本地临时存储最近日志
  - **备份策略**: NAS自动备份到异地存储
- **日志解析**: 自动解析测试结果关键信息并建立索引

#### 3.2.2 日志内容标准化
- **基本信息**:
  - 测试时间
  - 测试设备信息
  - 测试环境参数
- **测试项目**:
  - 容量测试
  - 读写速度测试
  - 坏块检测
  - 稳定性测试
  - 兼容性测试
- **测试结果**:
  - 通过/失败状态
  - 具体测试数值
  - 错误信息

#### 3.2.3 NAS存储管理
- **存储结构设计**:
  - 按年/月/日分层目录结构
  - 按批次号分类存储
  - 原始日志与解析结果分离存储
- **文件命名规范**:
  - 格式: {UID}_{TestType}_{Timestamp}.{ext}
  - 示例: 20241222-A001-0001_FullTest_20241222143022.log
- **存储优化**:
  - 自动压缩历史日志文件
  - 冷热数据分层存储
  - 定期清理临时文件
- **访问控制**:
  - 基于角色的NAS访问权限
  - 日志文件只读保护
  - 操作审计日志记录

#### 3.2.4 日志与样品关联
- 通过UID自动关联样品与测试日志
- 支持一个样品多次测试记录
- 支持测试记录的版本管理
- NAS路径与数据库记录关联映射

### 3.3 数据查询与分析模块

#### 3.3.1 查询功能
- **基础查询**:
  - 按UID查询
  - 按批次查询
  - 按时间范围查询
  - 按测试状态查询
- **高级查询**:
  - 多条件组合查询
  - 模糊查询
  - 正则表达式查询

#### 3.3.2 数据分析
- **统计分析**:
  - 测试通过率统计
  - 批次质量分析
  - 测试时间分析
- **趋势分析**:
  - 质量趋势图表
  - 性能对比分析
  - 异常数据识别

#### 3.3.3 报表生成
- **标准报表**:
  - 日测试报告
  - 周测试汇总
  - 月度质量报告
  - 批次测试报告
- **自定义报表**: 支持用户自定义报表模板

#### 3.3.4 批次测试报告详细功能
- **报告生成触发条件**:
  - 批次测试完成后自动生成
  - 手动选择批次生成报告
  - 定时批量生成报告
  - API接口触发生成

- **报告内容结构**:
  - **批次基本信息**:
    - 批次号、测试日期范围
    - 样品总数、测试设备信息
    - 测试工程师、审核人员
  - **测试统计汇总**:
    - 总体通过率、失败率
    - 各测试项目通过率统计
    - 测试时间统计（平均、最长、最短）
    - 异常样品数量及占比
  - **详细测试数据**:
    - 每个样品的UID及测试结果
    - 失败样品的详细错误信息
    - 性能数据分布图表
    - 质量等级分布
  - **质量分析**:
    - 与历史批次对比分析
    - 质量趋势分析
    - 问题分类统计
    - 改进建议

- **报告格式支持**:
  - **PDF格式**: 标准格式报告，支持打印
  - **Excel格式**: 包含详细数据，支持二次分析
  - **Word格式**: 可编辑的报告模板
  - **HTML格式**: 在线查看，支持交互图表

- **报告模板管理**:
  - **标准模板**: 系统预置的标准报告模板
  - **自定义模板**: 支持用户自定义报告格式
  - **模板版本控制**: 支持模板的版本管理
  - **模板权限管理**: 不同角色可使用不同模板

- **报告分发功能**:
  - **邮件发送**: 自动发送报告给相关人员
  - **系统通知**: 报告生成完成后系统内通知
  - **文件存储**: 报告自动存储到指定目录
  - **权限控制**: 基于角色的报告访问权限

- **报告查询与管理**:
  - **历史报告查询**: 按批次、时间、状态查询历史报告
  - **报告对比**: 支持多个批次报告的对比分析
  - **报告归档**: 长期报告的归档管理
  - **报告审核**: 支持报告的审核流程

### 3.4 系统管理模块

#### 3.4.1 用户管理
- **角色定义**:
  - 管理员: 系统全部权限
  - 测试工程师: 测试数据录入和查询
  - 质量工程师: 数据分析和报表生成
  - 访客: 只读权限
- **权限控制**: 基于角色的访问控制(RBAC)

#### 3.4.2 系统配置
- 测试项目配置
- 报表模板配置
- 数据备份配置
- 系统参数配置

## 4. 非功能需求

### 4.1 性能需求
- **响应时间**: 页面响应时间 < 3秒
- **并发用户**: 支持50个并发用户
- **数据处理**: 支持单次上传1000条测试记录
- **存储容量**: 支持至少100万条测试记录

### 4.2 可靠性需求
- **系统可用性**: 99.5%
- **数据完整性**: 确保数据不丢失、不损坏
- **故障恢复**: 系统故障后能在30分钟内恢复

### 4.3 安全性需求
- **数据加密**: 敏感数据传输和存储加密
- **访问控制**: 严格的用户身份验证和授权
- **操作审计**: 记录所有关键操作日志

### 4.4 易用性需求
- **界面友好**: 简洁直观的用户界面
- **操作简单**: 核心功能操作步骤不超过3步
- **帮助文档**: 提供完整的用户手册和在线帮助

## 5. 技术需求

### 5.1 系统架构
- **架构模式**: B/S架构，支持Web访问
- **技术栈建议**:
  - 前端: React/Vue.js + Ant Design 或 Blazor Server/WebAssembly
  - 后端: ASP.NET Core Web API
  - ORM框架: Entity Framework Core
  - 数据库: SQL Server/MySQL/PostgreSQL
  - 缓存: Redis
  - 消息队列: RabbitMQ (可选)
  - 日志框架: Serilog
  - 认证授权: ASP.NET Core Identity + JWT

### 5.2 接口需求
- **REST API**: 提供标准的RESTful接口
- **文件上传**: 支持大文件分片上传
- **数据导出**: 支持Excel、CSV、PDF格式导出
- **报告生成API**: 提供批次报告生成的API接口
- **WebAPI规范**: 遵循RESTful设计原则，支持Swagger文档

### 5.3 NAS存储集成技术需求
- **网络协议支持**:
  - SMB/CIFS协议 (Windows网络共享)
  - NFS协议 (Linux/Unix网络文件系统)
  - FTP/SFTP协议 (文件传输)
- **C#集成方案**:
  - System.IO.Directory/File处理网络路径
  - 使用UNC路径访问: \\\\NAS-Server\\ShareFolder
  - 网络驱动器映射和访问
  - 异步文件操作: FileStream.ReadAsync/WriteAsync
- **连接管理**:
  - 连接池管理网络连接
  - 断线重连机制
  - 超时和重试策略
  - 网络状态监控
- **性能优化**:
  - 批量文件操作
  - 并发上传/下载控制
  - 文件传输进度跟踪
  - 网络带宽限制

### 5.3 报告生成技术需求
- **报告引擎**:
  - PDF生成: 使用iTextSharp/iText7 for .NET
  - Excel生成: 使用EPPlus或NPOI
  - Word生成: 使用DocumentFormat.OpenXml
  - 图表生成: 使用Chart.js (前端) 或 System.Drawing (后端)
- **模板引擎**:
  - 使用Razor Pages模板引擎
  - 支持Liquid模板引擎 (DotLiquid)
  - 支持动态数据绑定和条件渲染
  - 支持循环和复杂逻辑处理
- **异步处理**:
  - 使用BackgroundService进行异步报告生成
  - Hangfire作业调度和队列管理
  - SignalR实现实时进度跟踪
  - Task并行库(TPL)优化性能
- **文件存储**:
  - **主存储**: NAS网络存储系统
  - **本地缓存**: 系统临时存储
  - **文件操作**: System.IO处理NAS文件访问
  - **压缩归档**: System.IO.Compression处理历史数据
  - **备份策略**: NAS RAID + 异地备份

### 5.4 部署需求
- **应用服务器部署**:
  - Docker容器化部署
  - IIS部署 (Windows Server)
  - Kestrel自托管部署
- **运行环境**:
  - .NET 8.0 Runtime
  - Windows Server 2019+ 或 Linux (Ubuntu 20.04+)
  - 最低4GB RAM，推荐8GB+
- **数据库部署**:
  - SQL Server 2019+ (推荐)
  - MySQL 8.0+ 或 PostgreSQL 13+
  - Redis 6.0+ (缓存)
- **NAS存储部署**:
  - **硬件要求**:
    - 专用NAS设备 (如Synology、QNAP等)
    - 最低2TB存储空间，推荐10TB+
    - RAID 5/6配置保证数据安全
    - 千兆网络接口，推荐万兆
  - **网络配置**:
    - 独立VLAN网络隔离
    - 固定IP地址配置
    - 防火墙端口开放 (SMB:445, NFS:2049)
  - **权限配置**:
    - 创建专用服务账户
    - 配置读写权限
    - 启用访问日志记录
- **数据备份策略**:
  - 数据库: SQL Server自动备份
  - NAS存储: RAID冗余 + 异地备份
  - 系统配置: 定期配置备份

### 5.5 开发工具和环境
- **开发IDE**: Visual Studio 2022 或 Visual Studio Code
- **版本控制**: Git + Azure DevOps 或 GitHub
- **包管理**: NuGet包管理器
- **单元测试**: xUnit.net 或 MSTest
- **API测试**: Postman 或 Swagger UI
- **性能监控**: Application Insights (可选)
- **代码质量**: SonarQube (可选)

## 6. 实施计划

### 6.1 开发阶段
- **第一阶段** (4周): 基础框架搭建，样品管理模块
  - ASP.NET Core项目初始化
  - Entity Framework Core数据模型设计
  - 基础CRUD API开发
  - 样品管理前端页面
- **第二阶段** (4周): 测试日志管理模块，UID管理
  - 文件上传处理服务
  - 日志解析引擎开发
  - UID生成和管理服务
  - 数据关联逻辑实现
- **第三阶段** (3周): 数据查询分析模块
  - 复杂查询API开发
  - 数据统计分析服务
  - 前端图表组件集成
- **第四阶段** (3周): 批次测试报告生成功能
  - Razor模板报告引擎
  - EPPlus/iTextSharp集成
  - Hangfire异步任务处理
  - SignalR实时进度推送
- **第五阶段** (2周): 系统管理，用户权限
  - ASP.NET Core Identity集成
  - JWT认证授权实现
  - 系统配置管理
- **第六阶段** (2周): 系统测试，部署上线
  - 单元测试和集成测试
  - Docker容器化配置
  - 生产环境部署

### 6.2 验收标准
- 功能完整性: 所有需求功能正常运行
- 性能达标: 满足性能需求指标
- 数据准确性: 测试数据准确无误
- 用户满意度: 用户培训后能熟练使用

## 7. 风险评估

### 7.1 技术风险
- **数据迁移**: 现有数据迁移可能存在格式不兼容问题
- **系统集成**: 与现有测试设备集成可能存在兼容性问题

### 7.2 业务风险
- **用户接受度**: 用户可能需要时间适应新系统
- **流程变更**: 现有工作流程需要相应调整

### 7.3 风险应对
- 提前进行技术调研和原型验证
- 制定详细的数据迁移方案
- 加强用户培训和技术支持

## 8. 预期效益

### 8.1 效率提升
- 减少人工录入时间80%
- 提高数据查询效率90%
- 降低数据错误率95%
- 批次报告生成时间从2小时缩短到5分钟
- 报告标准化程度提升，减少人工整理时间90%

### 8.2 管理优化
- 实现测试过程全程可追溯
- 提供数据驱动的质量分析
- 支持快速决策和问题定位

### 8.3 成本节约
- 减少人工成本
- 降低错误成本
- 提高设备利用率