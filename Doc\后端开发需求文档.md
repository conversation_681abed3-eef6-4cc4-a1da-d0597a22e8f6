# SD卡/U盘开卡测试管理系统 - 后端开发需求文档

## 1. 项目概述

### 1.1 系统定位
后端系统作为整个测试管理平台的核心，负责数据存储、业务逻辑处理、API服务提供和NAS存储管理。

### 1.2 技术架构
- **框架**: ASP.NET Core 8.0 Web API
- **ORM**: Entity Framework Core
- **数据库**: SQL Server 2019+ (主库) + Redis (缓存)
- **认证**: ASP.NET Core Identity + JWT
- **日志**: Serilog
- **任务调度**: Hangfire

## 2. 核心功能需求

### 2.1 样品管理API

#### 2.1.1 样品信息管理
```csharp
// API端点设计
POST   /api/samples              // 创建样品
GET    /api/samples              // 查询样品列表
GET    /api/samples/{id}         // 获取样品详情
PUT    /api/samples/{id}         // 更新样品信息
DELETE /api/samples/{id}         // 删除样品
POST   /api/samples/batch        // 批量创建样品
```

#### 2.1.2 UID管理服务
- **UID生成规则**: YYYYMMDD-BATCH-NNNN
- **唯一性验证**: 数据库约束 + 应用层验证
- **UID写入服务**: 提供UID写入样品的接口
- **批量UID生成**: 支持批量生成和分配UID

#### 2.1.3 样品状态管理
```csharp
public enum SampleStatus
{
    待测试 = 0,
    测试中 = 1,
    测试完成 = 2,
    测试失败 = 3,
    已出货 = 4
}
```

### 2.2 测试日志管理API

#### 2.2.1 日志上传接口
```csharp
POST /api/logs/upload           // 单文件上传
POST /api/logs/upload/batch     // 批量文件上传
POST /api/logs/upload/chunk     // 分片上传
GET  /api/logs/upload/status/{id} // 上传状态查询
```

#### 2.2.2 NAS存储集成
- **存储路径管理**: 按年/月/日/批次分层存储
- **文件命名规范**: {UID}_{TestType}_{Timestamp}.{ext}
- **存储服务**: 
  - 文件上传到NAS
  - 文件路径数据库记录
  - 文件访问权限控制
- **存储优化**:
  - 历史文件自动压缩
  - 冷热数据分层
  - 定期清理临时文件

#### 2.2.3 日志解析服务
- **支持格式**: TXT, LOG, CSV, JSON
- **解析引擎**: 可配置的解析规则
- **数据提取**: 测试结果、时间戳、设备信息等
- **异常处理**: 格式错误、数据缺失的处理

### 2.3 数据查询与分析API

#### 2.3.1 查询接口设计
```csharp
GET /api/data/search            // 综合查询
GET /api/data/samples           // 样品查询
GET /api/data/logs              // 日志查询
GET /api/data/statistics        // 统计数据
GET /api/data/trends            // 趋势分析
```

#### 2.3.2 查询参数支持
- **基础查询**: UID、批次、时间范围、状态
- **高级查询**: 多条件组合、模糊查询、正则表达式
- **分页排序**: 支持分页和多字段排序
- **性能优化**: 索引优化、查询缓存

### 2.4 报表生成服务

#### 2.4.1 报表生成API
```csharp
POST /api/reports/generate      // 生成报表
GET  /api/reports/{id}          // 获取报表
GET  /api/reports/{id}/download // 下载报表
GET  /api/reports/templates     // 报表模板管理
```

#### 2.4.2 报表引擎
- **PDF生成**: iText7 for .NET
- **Excel生成**: EPPlus
- **Word生成**: DocumentFormat.OpenXml
- **模板引擎**: Razor Pages
- **异步处理**: Hangfire后台任务
- **进度跟踪**: SignalR实时推送

## 3. 数据模型设计

### 3.1 核心实体模型

#### 3.1.1 样品实体 (Sample)
```csharp
public class Sample
{
    public int Id { get; set; }
    public string UID { get; set; }           // 唯一标识
    public string BatchNumber { get; set; }   // 批次号
    public string Model { get; set; }         // 型号
    public DateTime ProductionDate { get; set; } // 生产日期
    public string Supplier { get; set; }      // 供应商
    public long ExpectedCapacity { get; set; } // 预期容量
    public SampleStatus Status { get; set; }  // 状态
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // 导航属性
    public ICollection<TestLog> TestLogs { get; set; }
}
```

#### 3.1.2 测试日志实体 (TestLog)
```csharp
public class TestLog
{
    public int Id { get; set; }
    public string SampleUID { get; set; }     // 关联样品UID
    public string LogFileName { get; set; }   // 日志文件名
    public string NASFilePath { get; set; }   // NAS存储路径
    public string TestType { get; set; }      // 测试类型
    public DateTime TestTime { get; set; }    // 测试时间
    public bool TestResult { get; set; }      // 测试结果
    public string TestData { get; set; }      // 测试数据JSON
    public string ErrorMessage { get; set; }  // 错误信息
    public DateTime CreatedAt { get; set; }
    
    // 导航属性
    public Sample Sample { get; set; }
}
```

### 3.2 数据库设计要点
- **索引策略**: UID、BatchNumber、TestTime等关键字段
- **分区策略**: 按时间分区存储历史数据
- **约束设计**: UID唯一约束、外键约束
- **性能优化**: 读写分离、连接池配置

## 4. 接口规范

### 4.1 RESTful API设计原则
- **HTTP方法**: GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- **状态码**: 200(成功)、201(创建)、400(请求错误)、401(未授权)、404(未找到)、500(服务器错误)
- **响应格式**: 统一JSON格式

### 4.2 统一响应格式
```csharp
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public T Data { get; set; }
    public int Code { get; set; }
    public DateTime Timestamp { get; set; }
}
```

### 4.3 分页响应格式
```csharp
public class PagedResponse<T>
{
    public List<T> Items { get; set; }
    public int TotalCount { get; set; }
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}
```

## 5. 安全与认证

### 5.1 身份认证
- **JWT Token**: 基于JWT的无状态认证
- **Token刷新**: 支持Token自动刷新机制
- **多端登录**: 支持Web端和桌面端同时登录

### 5.2 权限控制
- **基于角色**: 管理员、测试工程师、质量工程师、访客
- **API权限**: 基于角色的API访问控制
- **数据权限**: 基于用户的数据访问范围控制

### 5.3 安全措施
- **HTTPS**: 强制HTTPS通信
- **CORS**: 跨域请求控制
- **限流**: API请求频率限制
- **日志审计**: 关键操作日志记录

## 6. 性能与监控

### 6.1 性能要求
- **响应时间**: API响应时间 < 500ms
- **并发处理**: 支持100个并发请求
- **数据处理**: 单次处理1000条记录
- **文件上传**: 支持GB级文件上传

### 6.2 缓存策略
- **Redis缓存**: 热点数据缓存
- **查询缓存**: 复杂查询结果缓存
- **文件缓存**: 报表文件临时缓存

### 6.3 监控指标
- **系统监控**: CPU、内存、磁盘使用率
- **应用监控**: API响应时间、错误率
- **业务监控**: 上传成功率、处理速度

## 7. 部署与运维

### 7.1 部署环境
- **容器化**: Docker部署
- **负载均衡**: 支持多实例部署
- **数据库**: SQL Server集群部署
- **缓存**: Redis集群部署

### 7.2 配置管理
- **环境配置**: 开发、测试、生产环境配置
- **连接字符串**: 数据库、Redis、NAS连接配置
- **业务配置**: UID生成规则、文件存储路径等

### 7.3 日志管理
- **结构化日志**: 使用Serilog结构化日志
- **日志级别**: Debug、Info、Warning、Error、Fatal
- **日志存储**: 文件日志 + 数据库日志
- **日志分析**: 支持日志查询和分析

## 8. 开发规范

### 8.1 代码规范
- **命名规范**: Pascal命名法、驼峰命名法
- **注释规范**: XML文档注释
- **异常处理**: 统一异常处理机制
- **单元测试**: 核心业务逻辑单元测试覆盖率 > 80%

### 8.2 开发工具
- **IDE**: Visual Studio 2022
- **版本控制**: Git
- **包管理**: NuGet
- **API文档**: Swagger/OpenAPI

### 8.3 质量保证
- **代码审查**: Pull Request代码审查
- **自动化测试**: 单元测试、集成测试
- **性能测试**: API性能测试
- **安全测试**: 安全漏洞扫描
