# SD卡/U盘开卡测试管理系统 - 量产工具通讯接口规范

## 1. 接口概述

### 1.1 通讯目标
量产工具需要与桌面端数据中转工具建立通信连接，在测试完成后将测试结果和日志数据发送给桌面端，由桌面端负责上传到管理系统。

### 1.2 通讯架构
```
┌─────────────────┐    通讯协议    ┌─────────────────┐
│   量产工具      │◄─────────────►│   桌面端应用    │
│                 │    数据传输    │                 │
└─────────────────┘               └─────────────────┘
```

### 1.3 支持的通讯协议
- **TCP Socket**: 推荐协议，可靠性高，支持大数据传输
- **命名管道**: 本机通信，性能优异
- **文件共享**: 通过监控共享文件夹进行数据交换
- **UDP Socket**: 可选协议，适用于实时性要求高的场景

## 2. TCP Socket通讯协议 (推荐)

### 2.1 连接建立
- **服务端**: 桌面端应用作为服务端，监听指定端口
- **客户端**: 量产工具作为客户端，主动连接桌面端
- **默认端口**: 8888 (可配置)
- **连接超时**: 30秒

### 2.2 握手协议
```json
// 1. 量产工具发送握手请求
{
  "messageType": "HANDSHAKE_REQUEST",
  "messageId": "uuid-string",
  "timestamp": "2024-12-22T14:30:22.123Z",
  "clientInfo": {
    "toolName": "ProductionTool_V1.0",
    "toolVersion": "1.0.0",
    "machineId": "MACHINE_001",
    "capabilities": ["FULL_TEST", "QUICK_TEST"]
  }
}

// 2. 桌面端返回握手响应
{
  "messageType": "HANDSHAKE_RESPONSE",
  "messageId": "uuid-string",
  "timestamp": "2024-12-22T14:30:22.456Z",
  "status": "SUCCESS",
  "serverInfo": {
    "serverName": "DataTransferTool",
    "serverVersion": "1.0.0",
    "supportedProtocols": ["TCP", "NAMED_PIPE"]
  }
}
```

### 2.3 心跳机制
```json
// 每30秒发送心跳包
{
  "messageType": "HEARTBEAT",
  "messageId": "uuid-string",
  "timestamp": "2024-12-22T14:30:22.123Z",
  "status": "ALIVE"
}

// 心跳响应
{
  "messageType": "HEARTBEAT_ACK",
  "messageId": "uuid-string",
  "timestamp": "2024-12-22T14:30:22.456Z",
  "status": "OK"
}
```

## 3. 数据传输协议

### 3.1 消息格式
所有消息采用以下格式：
```
[消息头(32字节)] + [消息体(变长)]
```

#### 3.1.1 消息头格式
```csharp
public struct MessageHeader
{
    public uint Magic;           // 魔数: 0x12345678
    public uint Version;         // 协议版本: 1
    public uint MessageType;     // 消息类型
    public uint MessageLength;   // 消息体长度
    public uint Checksum;        // CRC32校验码
    public uint Reserved1;       // 保留字段
    public uint Reserved2;       // 保留字段
    public uint Reserved3;       // 保留字段
}
```

#### 3.1.2 消息类型定义
```csharp
public enum MessageType : uint
{
    HANDSHAKE_REQUEST = 0x0001,     // 握手请求
    HANDSHAKE_RESPONSE = 0x0002,    // 握手响应
    HEARTBEAT = 0x0003,             // 心跳包
    HEARTBEAT_ACK = 0x0004,         // 心跳响应
    DATA_TRANSFER = 0x0005,         // 数据传输
    DATA_ACK = 0x0006,              // 数据确认
    ERROR = 0x0007,                 // 错误消息
    DISCONNECT = 0x0008             // 断开连接
}
```

### 3.2 测试数据传输
```json
{
  "messageType": "DATA_TRANSFER",
  "messageId": "uuid-string",
  "timestamp": "2024-12-22T14:30:22.123Z",
  "batchId": "BATCH_20241222_001",
  "testData": {
    "uid": "20241222-A001-0001",
    "testType": "FULL_TEST",
    "testStartTime": "2024-12-22T14:25:00.000Z",
    "testEndTime": "2024-12-22T14:30:00.000Z",
    "testResult": "PASS",
    "deviceInfo": {
      "stationId": "STATION_001",
      "operatorId": "OP001",
      "softwareVersion": "1.0.0"
    },
    "testResults": {
      "capacityTest": {
        "result": "PASS",
        "expectedCapacity": 32000000000,
        "actualCapacity": 31999234567,
        "tolerance": 0.1
      },
      "speedTest": {
        "result": "PASS",
        "readSpeed": 95.5,
        "writeSpeed": 87.3,
        "unit": "MB/s"
      },
      "badBlockTest": {
        "result": "PASS",
        "badBlockCount": 0,
        "maxAllowedBadBlocks": 10
      }
    },
    "logFiles": [
      {
        "fileName": "20241222-A001-0001_FullTest_20241222143022.log",
        "fileSize": 1024567,
        "fileType": "LOG",
        "encoding": "UTF-8",
        "content": "base64-encoded-content"
      },
      {
        "fileName": "20241222-A001-0001_FullTest_20241222143022.csv",
        "fileSize": 2048,
        "fileType": "CSV",
        "encoding": "UTF-8",
        "content": "base64-encoded-content"
      }
    ]
  }
}
```

### 3.3 数据确认机制
```json
// 桌面端发送确认消息
{
  "messageType": "DATA_ACK",
  "messageId": "original-message-id",
  "timestamp": "2024-12-22T14:30:25.123Z",
  "status": "SUCCESS",
  "processedItems": {
    "uid": "20241222-A001-0001",
    "dataReceived": true,
    "dataValidated": true,
    "uploadStarted": true
  },
  "errors": []
}

// 如果有错误
{
  "messageType": "DATA_ACK",
  "messageId": "original-message-id",
  "timestamp": "2024-12-22T14:30:25.123Z",
  "status": "ERROR",
  "errors": [
    {
      "errorCode": "INVALID_UID",
      "errorMessage": "UID格式不正确",
      "field": "testData.uid"
    }
  ]
}
```

## 4. 命名管道通讯协议

### 4.1 管道配置
- **管道名称**: `\\.\pipe\ProductionDataTransfer`
- **访问模式**: 双向通信
- **缓冲区大小**: 64KB
- **超时时间**: 30秒

### 4.2 连接建立
```csharp
// 桌面端创建命名管道服务器
var pipeServer = new NamedPipeServerStream(
    "ProductionDataTransfer",
    PipeDirection.InOut,
    maxNumberOfServerInstances: 10,
    PipeTransmissionMode.Message,
    PipeOptions.Asynchronous
);

// 量产工具连接到命名管道
var pipeClient = new NamedPipeClientStream(
    ".",
    "ProductionDataTransfer",
    PipeDirection.InOut,
    PipeOptions.Asynchronous
);
```

### 4.3 数据格式
命名管道使用与TCP Socket相同的消息格式和协议。

## 5. 文件共享通讯协议

### 5.1 共享目录结构
```
SharedFolder/
├── Input/          # 量产工具写入数据
│   ├── pending/    # 待处理文件
│   └── processing/ # 正在处理文件
├── Output/         # 桌面端写入响应
│   ├── success/    # 处理成功
│   └── error/      # 处理失败
└── Config/         # 配置文件
    └── settings.json
```

### 5.2 文件命名规范
```
输入文件: {UID}_{Timestamp}_{MessageId}.json
输出文件: {UID}_{Timestamp}_{MessageId}_response.json
```

### 5.3 数据文件格式
输入文件内容与TCP协议的JSON格式相同，输出文件为确认响应格式。

## 6. 错误处理

### 6.1 错误代码定义
```csharp
public enum ErrorCode
{
    SUCCESS = 0,
    INVALID_MESSAGE_FORMAT = 1001,
    INVALID_UID = 1002,
    INVALID_TEST_DATA = 1003,
    FILE_TOO_LARGE = 1004,
    UNSUPPORTED_FILE_TYPE = 1005,
    NETWORK_ERROR = 2001,
    SERVER_BUSY = 2002,
    AUTHENTICATION_FAILED = 2003,
    PERMISSION_DENIED = 2004,
    INTERNAL_ERROR = 5001
}
```

### 6.2 错误消息格式
```json
{
  "messageType": "ERROR",
  "messageId": "uuid-string",
  "timestamp": "2024-12-22T14:30:22.123Z",
  "errorCode": 1002,
  "errorMessage": "UID格式不正确",
  "details": {
    "field": "testData.uid",
    "value": "invalid-uid",
    "expectedFormat": "YYYYMMDD-BATCH-NNNN"
  }
}
```

## 7. 实现示例

### 7.1 量产工具端实现示例 (C++)
```cpp
class ProductionToolClient {
private:
    SOCKET clientSocket;
    std::string serverIP;
    int serverPort;
    
public:
    bool Connect(const std::string& ip, int port);
    bool SendHandshake();
    bool SendTestData(const TestData& data);
    bool ReceiveAcknowledgment();
    void Disconnect();
};

// 使用示例
ProductionToolClient client;
if (client.Connect("127.0.0.1", 8888)) {
    if (client.SendHandshake()) {
        TestData data = CollectTestData();
        if (client.SendTestData(data)) {
            client.ReceiveAcknowledgment();
        }
    }
    client.Disconnect();
}
```

### 7.2 量产工具端实现示例 (C#)
```csharp
public class ProductionToolClient
{
    private TcpClient tcpClient;
    private NetworkStream stream;
    
    public async Task<bool> ConnectAsync(string serverIP, int port)
    {
        try
        {
            tcpClient = new TcpClient();
            await tcpClient.ConnectAsync(serverIP, port);
            stream = tcpClient.GetStream();
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接失败: {ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> SendTestDataAsync(TestData testData)
    {
        try
        {
            var message = CreateDataMessage(testData);
            var jsonData = JsonSerializer.Serialize(message);
            var buffer = Encoding.UTF8.GetBytes(jsonData);
            
            await stream.WriteAsync(buffer, 0, buffer.Length);
            
            // 等待确认
            var response = await ReceiveResponseAsync();
            return response.Status == "SUCCESS";
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发送数据失败: {ex.Message}");
            return false;
        }
    }
}
```

### 7.3 Python实现示例
```python
import socket
import json
import uuid
from datetime import datetime

class ProductionToolClient:
    def __init__(self):
        self.socket = None
        
    def connect(self, server_ip, server_port):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((server_ip, server_port))
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def send_test_data(self, test_data):
        try:
            message = {
                "messageType": "DATA_TRANSFER",
                "messageId": str(uuid.uuid4()),
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "testData": test_data
            }
            
            json_data = json.dumps(message)
            self.socket.send(json_data.encode('utf-8'))
            
            # 接收确认
            response = self.socket.recv(4096)
            response_data = json.loads(response.decode('utf-8'))
            
            return response_data.get('status') == 'SUCCESS'
        except Exception as e:
            print(f"发送数据失败: {e}")
            return False
    
    def disconnect(self):
        if self.socket:
            self.socket.close()
```

## 8. 测试与验证

### 8.1 连接测试
- 验证TCP连接建立和断开
- 验证命名管道连接
- 验证文件共享监控

### 8.2 数据传输测试
- 小数据量传输测试
- 大数据量传输测试
- 并发传输测试
- 网络异常恢复测试

### 8.3 协议兼容性测试
- 不同版本协议兼容性
- 错误数据格式处理
- 异常情况处理

## 9. 部署配置

### 9.1 网络配置
- 确保防火墙开放通信端口
- 配置网络访问权限
- 设置网络超时参数

### 9.2 安全配置
- 配置访问控制列表
- 设置数据传输加密 (可选)
- 配置审计日志

### 9.3 性能调优
- 调整缓冲区大小
- 设置合适的超时时间
- 配置并发连接数限制

## 10. 故障排除

### 10.1 常见问题
- 连接超时: 检查网络连接和防火墙设置
- 数据格式错误: 验证JSON格式和字段完整性
- 文件传输失败: 检查文件大小和编码格式

### 10.2 调试工具
- 网络抓包工具 (Wireshark)
- TCP连接测试工具 (Telnet)
- JSON格式验证工具

### 10.3 日志分析
- 连接日志分析
- 数据传输日志分析
- 错误日志分析
